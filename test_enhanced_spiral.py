#!/usr/bin/env python3
"""
Test Enhanced Research Spiral Architecture
Quick verification of system components
"""

from enhanced_research_spiral import EnhancedResearchSpiral
import os

def test_system_initialization():
    """Test system initialization and configuration"""
    print("🧪 Testing Enhanced Research Spiral Initialization")
    print("=" * 50)
    
    try:
        spiral = EnhancedResearchSpiral(silent=False)
        
        print(f"✅ System initialized successfully")
        print(f"📋 Total agents: {spiral.total_agents}")
        print(f"🎯 Agent definitions: {len(spiral.agents)}")
        
        # Test agent definitions
        for agent_id, agent_info in spiral.agents.items():
            print(f"   Agent {agent_id}: {agent_info['name']}")
            print(f"      Tools: {', '.join(agent_info['primary_tools'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return False

def test_workspace_creation():
    """Test research workspace creation"""
    print("\n📁 Testing Research Workspace Creation")
    print("=" * 50)
    
    try:
        spiral = EnhancedResearchSpiral(silent=True)
        
        test_prompt = "Test research prompt for multimodal dataset distillation"
        workspace = spiral.initialize_research_environment(test_prompt)
        
        # Check if workspace was created
        if os.path.exists(workspace):
            print(f"✅ Workspace created: {workspace}")
            
            # Check required files
            required_files = [
                "research_prompt.md",
                "decision_log.json", 
                "reference_database.json",
                "quality_assessments.json",
                "research_state.json"
            ]
            
            for file in required_files:
                filepath = os.path.join(workspace, file)
                if os.path.exists(filepath):
                    print(f"   ✅ {file}")
                else:
                    print(f"   ❌ {file} missing")
            
            return True
        else:
            print(f"❌ Workspace not created")
            return False
            
    except Exception as e:
        print(f"❌ Workspace creation failed: {e}")
        return False

def test_knowledge_base_access():
    """Test knowledge base loading"""
    print("\n📚 Testing Knowledge Base Access")
    print("=" * 50)
    
    try:
        spiral = EnhancedResearchSpiral(silent=True)
        knowledge_files = spiral.load_knowledge_base()
        
        print(f"✅ Knowledge base loaded")
        print(f"📊 Total files: {len(knowledge_files)}")
        
        # Check for key files
        key_files = ["MMIS", "dataset distillation", "multimodal"]
        found_files = []
        
        for key in key_files:
            matching_files = [f for f in knowledge_files if key.lower() in f.lower()]
            if matching_files:
                found_files.extend(matching_files[:2])  # Show first 2 matches
        
        print(f"📋 Key research files found:")
        for file in found_files[:5]:  # Show first 5
            filename = os.path.basename(file)
            print(f"   • {filename}")
        
        return len(knowledge_files) > 0
        
    except Exception as e:
        print(f"❌ Knowledge base access failed: {e}")
        return False

def test_agent_prompt_generation():
    """Test agent prompt generation"""
    print("\n🤖 Testing Agent Prompt Generation")
    print("=" * 50)
    
    try:
        spiral = EnhancedResearchSpiral(silent=True)
        
        # Initialize workspace
        test_prompt = "Test multimodal dataset distillation research"
        workspace = spiral.initialize_research_environment(test_prompt)
        
        # Test prompt generation for each agent
        for agent_id in range(1, 4):  # Test first 3 agents
            prompt = spiral.create_agent_prompt(agent_id, test_prompt)
            
            agent_name = spiral.agents[agent_id]['name']
            print(f"✅ Agent {agent_id} ({agent_name})")
            print(f"   Prompt length: {len(prompt)} characters")
            
            # Check if prompt contains key elements
            key_elements = ["Role", "Tools", "Responsibilities", "Knowledge Base"]
            for element in key_elements:
                if element.lower() in prompt.lower():
                    print(f"   ✅ Contains {element}")
                else:
                    print(f"   ⚠️ Missing {element}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent prompt generation failed: {e}")
        return False

def test_single_agent_execution():
    """Test single agent execution (Research Orchestrator)"""
    print("\n🔄 Testing Single Agent Execution")
    print("=" * 50)
    
    try:
        spiral = EnhancedResearchSpiral(silent=False)
        
        # Initialize workspace
        test_prompt = "Brief test of multimodal dataset distillation research coordination"
        workspace = spiral.initialize_research_environment(test_prompt)
        
        print(f"🔄 Testing Research Orchestrator Agent (Agent 1)...")
        
        # Run Research Orchestrator Agent
        result = spiral.run_agent(1, test_prompt)
        
        if result['status'] == 'success':
            print(f"✅ Agent execution successful")
            print(f"⏱️ Execution time: {result['execution_time']:.1f}s")
            print(f"📝 Response length: {len(result['response'])} characters")
            print(f"📝 Response preview: {result['response'][:200]}...")
            
            # Save the output
            spiral.save_agent_output(result)
            print(f"✅ Agent output saved")
            
            return True
        else:
            print(f"❌ Agent execution failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Single agent test failed: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🔬 ENHANCED RESEARCH SPIRAL - SYSTEM VERIFICATION")
    print("=" * 70)
    
    tests = [
        ("System Initialization", test_system_initialization),
        ("Workspace Creation", test_workspace_creation),
        ("Knowledge Base Access", test_knowledge_base_access),
        ("Agent Prompt Generation", test_agent_prompt_generation),
        ("Single Agent Execution", test_single_agent_execution)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
            if results[test_name]:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 VERIFICATION SUMMARY")
    print("="*70)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed ({passed/len(tests)*100:.1f}%)")
    
    if passed == len(tests):
        print("🎉 ALL VERIFICATION TESTS PASSED!")
        print("🚀 Enhanced Research Spiral ready for full execution")
        print(f"\n📋 To run the complete research:")
        print(f"   python run_enhanced_research_spiral.py")
        return True
    else:
        print("⚠️ Some tests failed - system needs attention")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 System verified and ready!")
        print(f"🔄 Enhanced Research Spiral with 6 specialized agents")
        print(f"🛠️ Comprehensive tool integration confirmed")
        print(f"📚 Knowledge base access verified")
    else:
        print(f"\n🔧 Fix failed tests before running full research")
