#!/usr/bin/env python3
"""Test Gemini API setup and hybrid agent functionality"""

import yaml
import google.generativeai as genai
from hybrid_agent import HybridAgent

def test_gemini_api():
    """Test direct Gemini API connection"""
    print("🧪 Testing Gemini API Connection")
    print("=" * 50)
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Test Gemini API
        api_key = config['gemini']['api_key']
        model_name = config['gemini']['model']
        
        print(f"📋 API Key: {api_key[:10]}...{api_key[-10:]}")
        print(f"📋 Model: {model_name}")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(model_name)
        
        # Test simple generation
        response = model.generate_content("Hello, this is a test. Please respond with 'Gemini API working!'")
        
        if response.text:
            print(f"✅ Gemini API connection successful!")
            print(f"📝 Response: {response.text}")
            return True
        else:
            print("❌ Gemini API returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False

def test_hybrid_agent():
    """Test hybrid agent functionality"""
    print("\n🤖 Testing Hybrid Agent")
    print("=" * 50)
    
    try:
        # Create hybrid agent
        agent = HybridAgent(silent=False)
        
        print(f"✅ Hybrid agent initialized successfully!")
        print(f"📋 Agent type: {agent.get_agent_type()}")
        
        # Test simple query
        print("\n🔍 Testing simple query...")
        response = agent.run("Hello! Please introduce yourself and confirm you can access research tools.")
        
        if response:
            print(f"✅ Agent response received!")
            print(f"📝 Response preview: {response[:200]}...")
            return True
        else:
            print("❌ Agent returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Hybrid agent test failed: {e}")
        return False

def test_tool_discovery():
    """Test tool discovery functionality"""
    print("\n🛠️ Testing Tool Discovery")
    print("=" * 50)
    
    try:
        from tools import discover_tools
        
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Discover tools
        tools = discover_tools(config, silent=False)
        
        print(f"✅ Tool discovery successful!")
        print(f"📋 Found {len(tools)} tools:")
        
        for tool_name in tools.keys():
            print(f"   - {tool_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool discovery failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Enhanced Make It Heavy Framework - Gemini Setup Test")
    print("=" * 70)
    
    # Test 1: Gemini API
    gemini_success = test_gemini_api()
    
    # Test 2: Tool Discovery
    tools_success = test_tool_discovery()
    
    # Test 3: Hybrid Agent
    agent_success = test_hybrid_agent() if gemini_success else False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"✅ Gemini API: {'PASSED' if gemini_success else 'FAILED'}")
    print(f"✅ Tool Discovery: {'PASSED' if tools_success else 'FAILED'}")
    print(f"✅ Hybrid Agent: {'PASSED' if agent_success else 'FAILED'}")
    
    if gemini_success and tools_success and agent_success:
        print("\n🎉 All tests passed! System ready for small-scale testing.")
        print("\n📋 Next Steps:")
        print("1. Run small-scale test: python small_scale_test.py")
        print("2. Run full deployment: python make_it_heavy.py")
        return True
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        return False

if __name__ == "__main__":
    main()
