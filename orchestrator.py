import json
import yaml
import time
import threading
import os
import glob
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from datetime import datetime
from hybrid_agent import HybridAgent

# ============== CHECKPOINT MANAGEMENT ==============
class CheckpointManager:
    def __init__(self, checkpoint_dir: str = "./research_checkpoints"):
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)

    def save_checkpoint(self, session_id: str, data: dict):
        """Save checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            with open(checkpoint_file, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"💾 Checkpoint saved: {checkpoint_file}")
        except Exception as e:
            print(f"❌ Failed to save checkpoint: {e}")

    def load_checkpoint(self, session_id: str) -> dict:
        """Load checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            if os.path.exists(checkpoint_file):
                with open(checkpoint_file, 'r') as f:
                    data = json.load(f)
                print(f"📂 Checkpoint loaded: {checkpoint_file}")
                return data
        except Exception as e:
            print(f"❌ Failed to load checkpoint: {e}")
        return {}

    def list_checkpoints(self) -> List[str]:
        """List available checkpoints"""
        try:
            files = glob.glob(os.path.join(self.checkpoint_dir, "*_checkpoint.json"))
            return [os.path.basename(f).replace("_checkpoint.json", "") for f in files]
        except:
            return []

class TaskOrchestrator:
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)

        self.num_agents = self.config['orchestrator']['parallel_agents']
        self.task_timeout = self.config['orchestrator']['task_timeout']
        self.aggregation_strategy = self.config['orchestrator']['aggregation_strategy']
        self.max_concurrent = self.config['orchestrator'].get('max_concurrent', 1)  # Default to 1 for rate limits
        self.batch_delay = self.config['orchestrator'].get('batch_delay', 10)  # Default to 10 seconds
        self.silent = silent

        # Track agent progress
        self.agent_progress = {}
        self.agent_results = {}
        self.progress_lock = threading.Lock()

        # Checkpoint management
        self.checkpoint_manager = CheckpointManager()
        self.session_id = None
    
    def decompose_task(self, user_input: str, num_agents: int) -> List[str]:
        """Use AI to dynamically generate different questions based on user input"""
        
        # Create question generation agent
        question_agent = HybridAgent(silent=True)
        
        # Get question generation prompt from config
        prompt_template = self.config['orchestrator']['question_generation_prompt']
        generation_prompt = prompt_template.format(
            user_input=user_input,
            num_agents=num_agents
        )
        
        # Remove task completion tool to avoid issues (access the underlying agent)
        actual_agent = question_agent.agent
        if hasattr(actual_agent, 'tools'):
            actual_agent.tools = [tool for tool in actual_agent.tools if tool.get('function', {}).get('name') != 'mark_task_complete']
        if hasattr(actual_agent, 'tool_mapping'):
            actual_agent.tool_mapping = {name: func for name, func in actual_agent.tool_mapping.items() if name != 'mark_task_complete'}
        
        try:
            # Get AI-generated questions
            response = question_agent.run(generation_prompt)
            
            # Parse JSON response
            questions = json.loads(response.strip())
            
            # Validate we got the right number of questions
            if len(questions) != num_agents:
                raise ValueError(f"Expected {num_agents} questions, got {len(questions)}")
            
            return questions
            
        except (json.JSONDecodeError, ValueError) as e:
            # Fallback: create simple variations if AI fails
            return [
                f"Research comprehensive information about: {user_input}",
                f"Analyze and provide insights about: {user_input}",
                f"Find alternative perspectives on: {user_input}",
                f"Verify and cross-check facts about: {user_input}"
            ][:num_agents]
    
    def update_agent_progress(self, agent_id: int, status: str, result: str = None):
        """Thread-safe progress tracking"""
        with self.progress_lock:
            self.agent_progress[agent_id] = status
            if result is not None:
                self.agent_results[agent_id] = result
    
    def run_agent_parallel(self, agent_id: int, subtask: str) -> Dict[str, Any]:
        """
        Run a single agent with the given subtask.
        Returns result dictionary with agent_id, status, and response.
        """
        try:
            self.update_agent_progress(agent_id, "PROCESSING...")
            
            # Use hybrid agent
            agent = HybridAgent(silent=True)
            
            start_time = time.time()
            response = agent.run(subtask)
            execution_time = time.time() - start_time
            
            self.update_agent_progress(agent_id, "COMPLETED", response)
            
            return {
                "agent_id": agent_id,
                "status": "success", 
                "response": response,
                "execution_time": execution_time
            }
            
        except Exception as e:
            # Simple error handling
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "execution_time": 0
            }
    
    def aggregate_results(self, agent_results: List[Dict[str, Any]]) -> str:
        """
        Combine results from all agents into a comprehensive final answer.
        Uses the configured aggregation strategy.
        """
        successful_results = [r for r in agent_results if r["status"] == "success"]
        
        if not successful_results:
            return "All agents failed to provide results. Please try again."
        
        # Extract responses for aggregation
        responses = [r["response"] for r in successful_results]
        
        if self.aggregation_strategy == "consensus":
            return self._aggregate_consensus(responses, successful_results)
        else:
            # Default to consensus
            return self._aggregate_consensus(responses, successful_results)
    
    def _aggregate_consensus(self, responses: List[str], _results: List[Dict[str, Any]]) -> str:
        """
        Use one final AI call to synthesize all agent responses into a coherent answer.
        """
        if len(responses) == 1:
            return responses[0]
        
        # Create synthesis agent to combine all responses
        synthesis_agent = HybridAgent(silent=True)
        
        # Build agent responses section
        agent_responses_text = ""
        for i, response in enumerate(responses, 1):
            agent_responses_text += f"=== AGENT {i} RESPONSE ===\n{response}\n\n"
        
        # Get synthesis prompt from config and format it
        synthesis_prompt_template = self.config['orchestrator']['synthesis_prompt']
        synthesis_prompt = synthesis_prompt_template.format(
            num_responses=len(responses),
            agent_responses=agent_responses_text
        )
        
        # Completely remove all tools from synthesis agent to force direct response (access the underlying agent)
        actual_synthesis_agent = synthesis_agent.agent
        if hasattr(actual_synthesis_agent, 'tools'):
            actual_synthesis_agent.tools = []
        if hasattr(actual_synthesis_agent, 'tool_mapping'):
            actual_synthesis_agent.tool_mapping = {}
        
        # Get the synthesized response
        try:
            final_answer = synthesis_agent.run(synthesis_prompt)
            return final_answer
        except Exception as e:
            # Log the error for debugging
            print(f"\n🚨 SYNTHESIS FAILED: {str(e)}")
            print("📋 Falling back to concatenated responses\n")
            # Fallback: if synthesis fails, concatenate responses
            combined = []
            for i, response in enumerate(responses, 1):
                combined.append(f"=== Agent {i} Response ===")
                combined.append(response)
                combined.append("")
            return "\n".join(combined)
    
    def get_progress_status(self) -> Dict[int, str]:
        """Get current progress status for all agents"""
        with self.progress_lock:
            return self.agent_progress.copy()
    
    def orchestrate(self, user_input: str):
        """
        Main orchestration method.
        Takes user input, delegates to parallel agents, and returns aggregated result.
        """
        
        # Reset progress tracking
        self.agent_progress = {}
        self.agent_results = {}
        
        # Decompose task into subtasks
        subtasks = self.decompose_task(user_input, self.num_agents)
        
        # Initialize progress tracking
        for i in range(self.num_agents):
            self.agent_progress[i] = "QUEUED"
        
        # Execute agents in parallel
        agent_results = []
        
        with ThreadPoolExecutor(max_workers=self.num_agents) as executor:
            # Submit all agent tasks
            future_to_agent = {
                executor.submit(self.run_agent_parallel, i, subtasks[i]): i 
                for i in range(self.num_agents)
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_agent, timeout=self.task_timeout):
                try:
                    result = future.result()
                    agent_results.append(result)
                except Exception as e:
                    agent_id = future_to_agent[future]
                    agent_results.append({
                        "agent_id": agent_id,
                        "status": "timeout",
                        "response": f"Agent {agent_id + 1} timed out or failed: {str(e)}",
                        "execution_time": self.task_timeout
                    })
        
        # Sort results by agent_id for consistent output
        agent_results.sort(key=lambda x: x["agent_id"])
        
        # Aggregate results
        final_result = self.aggregate_results(agent_results)
        
        return final_result

    def set_session_id(self, session_id: str):
        """Set session ID for checkpoint management"""
        self.session_id = session_id

    def run_agent_with_timeout(self, agent_id: int, subtask: str, timeout: int = 600) -> Dict[str, Any]:
        """Run a single agent task with timeout handling and enhanced error recovery"""
        try:
            self.update_agent_progress(agent_id, "PROCESSING...")

            # Create agent with enhanced tools
            agent = HybridAgent(silent=True)

            start_time = time.time()
            response = agent.run(subtask)
            execution_time = time.time() - start_time

            # Check for timeout
            if execution_time > timeout:
                return {
                    "agent_id": agent_id,
                    "status": "timeout",
                    "response": f"Agent {agent_id} timed out after {timeout} seconds",
                    "execution_time": execution_time
                }

            self.update_agent_progress(agent_id, "COMPLETED", response)

            return {
                "agent_id": agent_id,
                "status": "success",
                "response": response,
                "execution_time": execution_time,
                "subtask": subtask
            }

        except Exception as e:
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "execution_time": time.time() - start_time if 'start_time' in locals() else 0,
                "subtask": subtask
            }

    def run_sequential_batch_orchestration(self, user_input: str, progress_callback=None) -> Dict[str, Any]:
        """Run large-scale sequential batch orchestration with checkpointing"""

        if not self.session_id:
            self.session_id = f"session_{int(time.time())}"

        if not self.silent:
            print(f"🚀 Starting {self.num_agents}-agent orchestration for: {user_input}")
            print(f"📊 Configuration: {self.max_concurrent} concurrent, {self.batch_delay}s batch delay")

        # Check for existing checkpoint
        checkpoint_data = self.checkpoint_manager.load_checkpoint(self.session_id)

        if checkpoint_data:
            if not self.silent:
                print(f"📂 Resuming from checkpoint with {len(checkpoint_data.get('completed_agents', []))} completed agents")
            completed_agents = checkpoint_data.get('completed_agents', [])
            agent_results = checkpoint_data.get('agent_results', [])
            subtasks = checkpoint_data.get('subtasks', [])
        else:
            if not self.silent:
                print("🔄 Generating research questions...")
            subtasks = self.decompose_task(user_input, self.num_agents)
            completed_agents = []
            agent_results = []

            # Save initial checkpoint
            self.checkpoint_manager.save_checkpoint(self.session_id, {
                'user_input': user_input,
                'subtasks': subtasks,
                'completed_agents': completed_agents,
                'agent_results': agent_results,
                'total_agents': self.num_agents
            })

        # Calculate batches
        remaining_agents = [i for i in range(self.num_agents) if i not in completed_agents]
        total_batches = (len(remaining_agents) + self.max_concurrent - 1) // self.max_concurrent

        if not self.silent:
            print(f"📋 Processing {len(remaining_agents)} remaining agents in {total_batches} batches")

        # Process agents in batches
        for batch_idx in range(total_batches):
            batch_start = batch_idx * self.max_concurrent
            batch_end = min(batch_start + self.max_concurrent, len(remaining_agents))
            batch_agents = remaining_agents[batch_start:batch_end]

            if not self.silent:
                print(f"\n🔄 Batch {batch_idx + 1}/{total_batches}: Processing agents {batch_agents}")

            # Run batch concurrently
            batch_results = []
            with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
                future_to_agent = {
                    executor.submit(self.run_agent_with_timeout, agent_id, subtasks[agent_id], self.task_timeout): agent_id
                    for agent_id in batch_agents
                }

                for future in as_completed(future_to_agent, timeout=self.task_timeout + 100):
                    try:
                        result = future.result()
                        batch_results.append(result)
                        agent_id = result['agent_id']
                        completed_agents.append(agent_id)

                        if not self.silent:
                            print(f"  ✅ Agent {agent_id} completed ({result['status']}) - {result['execution_time']:.1f}s")

                        # Update progress
                        if progress_callback:
                            progress_callback(len(completed_agents), self.num_agents, result)

                    except Exception as e:
                        agent_id = future_to_agent[future]
                        error_result = {
                            "agent_id": agent_id,
                            "subtask": subtasks[agent_id],
                            "response": f"Batch execution error: {str(e)}",
                            "status": "error",
                            "execution_time": self.task_timeout
                        }
                        batch_results.append(error_result)
                        completed_agents.append(agent_id)
                        if not self.silent:
                            print(f"  ❌ Agent {agent_id} failed: {str(e)}")

            # Add batch results to overall results
            agent_results.extend(batch_results)

            # Save checkpoint after each batch
            checkpoint_data = {
                'user_input': user_input,
                'subtasks': subtasks,
                'completed_agents': completed_agents,
                'agent_results': agent_results,
                'total_agents': self.num_agents,
                'batch_completed': batch_idx + 1,
                'total_batches': total_batches
            }
            self.checkpoint_manager.save_checkpoint(self.session_id, checkpoint_data)

            # Batch delay (except for last batch)
            if batch_idx < total_batches - 1:
                if not self.silent:
                    print(f"⏳ Waiting {self.batch_delay}s before next batch...")
                time.sleep(self.batch_delay)

        if not self.silent:
            print(f"\n🎉 All {self.num_agents} agents completed!")
            print(f"📊 Success: {len([r for r in agent_results if r['status'] == 'success'])}")
            print(f"❌ Errors: {len([r for r in agent_results if r['status'] == 'error'])}")
            print(f"⏰ Timeouts: {len([r for r in agent_results if r['status'] == 'timeout'])}")

        # Aggregate results
        if not self.silent:
            print("🔀 Synthesizing results...")
        final_synthesis = self.aggregate_results(agent_results)

        # Save final results
        final_data = checkpoint_data.copy()
        final_data['final_synthesis'] = final_synthesis
        final_data['completion_time'] = datetime.now().isoformat()
        self.checkpoint_manager.save_checkpoint(f"{self.session_id}_final", final_data)

        return {
            'user_input': user_input,
            'total_agents': self.num_agents,
            'agent_results': agent_results,
            'final_synthesis': final_synthesis,
            'session_id': self.session_id,
            'statistics': {
                'completed': len([r for r in agent_results if r['status'] == 'success']),
                'errors': len([r for r in agent_results if r['status'] == 'error']),
                'timeouts': len([r for r in agent_results if r['status'] == 'timeout']),
                'total_duration': sum(r['execution_time'] for r in agent_results),
                'avg_duration': sum(r['execution_time'] for r in agent_results) / len(agent_results) if agent_results else 0
            }
        }