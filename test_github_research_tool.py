#!/usr/bin/env python3
"""
GitHub Research Tool Test Script

This script tests the GitHub research tool functionality to ensure it can:
1. Search for research paper implementations
2. Find relevant repositories
3. Analyze repository structure
4. Extract paper links and implementation details

Usage:
    python test_github_research_tool.py [github_token]
"""

import sys
import time
from github_research_tool import GitHubResearchTool

def test_paper_implementation_search():
    """Test searching for paper implementations"""
    print("🧪 Testing paper implementation search...")
    
    test_queries = [
        "dataset distillation",
        "neural architecture search",
        "transformer attention",
        "few shot learning",
        "meta learning"
    ]
    
    results = []
    
    for query in test_queries:
        print(f"\n🔍 Testing query: '{query}'")
        
        result = tool.execute(
            query=query,
            search_type="papers",
            language="python",
            limit=3
        )
        
        if result['status'] == 'success':
            repos = result.get('repositories', [])
            print(f"   ✅ Found {len(repos)} paper implementations")
            
            for repo in repos[:2]:  # Show first 2
                score = repo.get('paper_relevance_score', 0)
                print(f"      - {repo['full_name']} (⭐{repo['stars']}, score: {score:.2f})")
                
                if repo.get('paper_links'):
                    print(f"        📄 Paper links: {len(repo['paper_links'])}")
                
                if repo.get('readme_preview'):
                    preview = repo['readme_preview'][:100] + "..." if len(repo['readme_preview']) > 100 else repo['readme_preview']
                    print(f"        📝 README: {preview}")
            
            results.append({
                'query': query,
                'success': True,
                'repos_found': len(repos)
            })
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
            results.append({
                'query': query,
                'success': False,
                'error': result.get('error')
            })
    
    return results

def test_code_search():
    """Test searching for specific code implementations"""
    print("\n🧪 Testing code search...")
    
    code_queries = [
        "class DatasetDistillation",
        "def attention_mechanism",
        "class Transformer",
        "def few_shot_learning"
    ]
    
    results = []
    
    for query in code_queries:
        print(f"\n🔍 Testing code query: '{query}'")
        
        result = tool.execute(
            query=query,
            search_type="code",
            language="python",
            limit=3
        )
        
        if result['status'] == 'success':
            files = result.get('code_files', [])
            print(f"   ✅ Found {len(files)} code files")
            
            for file_info in files[:2]:
                print(f"      - {file_info['repository']}/{file_info['path']}")
                if file_info.get('content_preview'):
                    preview = file_info['content_preview'][:80] + "..."
                    print(f"        💻 Preview: {preview}")
            
            results.append({
                'query': query,
                'success': True,
                'files_found': len(files)
            })
        else:
            print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
            results.append({
                'query': query,
                'success': False,
                'error': result.get('error')
            })
    
    return results

def test_repository_analysis():
    """Test repository analysis functionality"""
    print("\n🧪 Testing repository analysis...")
    
    # Test with a known research repository
    test_repos = [
        "pytorch/pytorch",
        "huggingface/transformers",
        "openai/gpt-2"
    ]
    
    for repo_name in test_repos:
        print(f"\n🔍 Analyzing repository: {repo_name}")
        
        try:
            # Test the analysis methods directly
            analysis = tool._get_repository_info(repo_name)
            
            print(f"   📋 Analysis results:")
            print(f"      - README available: {'✅' if analysis.get('readme_preview') else '❌'}")
            print(f"      - Paper links found: {len(analysis.get('paper_links', []))}")
            print(f"      - Key files: {len(analysis.get('key_files', []))}")
            print(f"      - Has requirements: {'✅' if analysis.get('has_requirements') else '❌'}")
            
            if analysis.get('paper_links'):
                print(f"      - Paper links: {analysis['paper_links'][:2]}")  # Show first 2
            
            if analysis.get('key_files'):
                print(f"      - Key files: {analysis['key_files'][:3]}")  # Show first 3
                
        except Exception as e:
            print(f"   ❌ Analysis failed: {str(e)}")

def test_rate_limiting():
    """Test API rate limiting behavior"""
    print("\n🧪 Testing rate limiting behavior...")
    
    start_time = time.time()
    requests_made = 0
    
    # Make several quick requests to test rate limiting
    for i in range(5):
        print(f"   Request {i+1}/5...")
        
        result = tool.execute(
            query=f"test query {i}",
            search_type="repositories",
            limit=1
        )
        
        requests_made += 1
        
        if result['status'] == 'error' and 'rate limit' in result.get('error', '').lower():
            print(f"   ⚠️ Hit rate limit after {requests_made} requests")
            break
        elif result['status'] == 'success':
            print(f"   ✅ Request successful")
        else:
            print(f"   ❌ Request failed: {result.get('error', 'Unknown')}")
        
        time.sleep(1)  # Small delay between requests
    
    end_time = time.time()
    print(f"   📊 Made {requests_made} requests in {end_time - start_time:.1f} seconds")

def analyze_results(paper_results, code_results):
    """Analyze test results and provide summary"""
    print("\n📊 TEST RESULTS ANALYSIS")
    print("=" * 50)
    
    # Paper search analysis
    paper_success = len([r for r in paper_results if r['success']])
    paper_total = len(paper_results)
    print(f"📄 Paper Implementation Search:")
    print(f"   Success rate: {paper_success}/{paper_total} ({paper_success/paper_total*100:.1f}%)")
    
    if paper_success > 0:
        avg_repos = sum(r.get('repos_found', 0) for r in paper_results if r['success']) / paper_success
        print(f"   Average repositories found: {avg_repos:.1f}")
    
    # Code search analysis
    code_success = len([r for r in code_results if r['success']])
    code_total = len(code_results)
    print(f"\n💻 Code Search:")
    print(f"   Success rate: {code_success}/{code_total} ({code_success/code_total*100:.1f}%)")
    
    if code_success > 0:
        avg_files = sum(r.get('files_found', 0) for r in code_results if r['success']) / code_success
        print(f"   Average files found: {avg_files:.1f}")
    
    # Overall assessment
    overall_success = (paper_success + code_success) / (paper_total + code_total)
    print(f"\n🎯 Overall Success Rate: {overall_success*100:.1f}%")
    
    if overall_success > 0.8:
        print("✅ GitHub Research Tool is working excellently!")
    elif overall_success > 0.6:
        print("✅ GitHub Research Tool is working well with minor issues")
    elif overall_success > 0.4:
        print("⚠️ GitHub Research Tool has some issues but is functional")
    else:
        print("❌ GitHub Research Tool needs attention")
    
    print("\n💡 Recommendations:")
    if paper_success < paper_total:
        print("   - Check GitHub API rate limits")
        print("   - Verify internet connection")
        print("   - Consider adding GitHub token for higher rate limits")
    
    if code_success < code_total:
        print("   - Code search may be more restrictive")
        print("   - Try more specific search queries")

def main():
    """Main test function"""
    global tool
    
    print("🚀 GitHub Research Tool Comprehensive Test")
    print("=" * 60)
    
    # Initialize tool
    github_token = sys.argv[1] if len(sys.argv) > 1 else None
    tool = GitHubResearchTool(github_token)
    
    if github_token:
        print(f"🔑 Using provided GitHub token")
    else:
        print("⚠️ No GitHub token provided - using unauthenticated access")
        print("💡 Usage: python test_github_research_tool.py YOUR_GITHUB_TOKEN")
    
    print(f"🌐 Testing GitHub API access...")
    
    try:
        # Run all tests
        paper_results = test_paper_implementation_search()
        code_results = test_code_search()
        test_repository_analysis()
        test_rate_limiting()
        
        # Analyze results
        analyze_results(paper_results, code_results)
        
        print("\n🎉 Testing completed!")
        print("\n🔗 Next steps:")
        print("   1. If tests passed: GitHub tool is ready for integration")
        print("   2. If rate limited: Add GitHub token for higher limits")
        print("   3. If errors: Check internet connection and GitHub API status")
        
    except Exception as e:
        print(f"\n💥 Test suite failed: {str(e)}")
        print("🔧 Troubleshooting:")
        print("   1. Check internet connection")
        print("   2. Verify GitHub API is accessible")
        print("   3. Check if GitHub token is valid (if provided)")

if __name__ == "__main__":
    main()
