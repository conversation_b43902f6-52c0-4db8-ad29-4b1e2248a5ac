#!/usr/bin/env python3
"""
Basic Gemini API Test
"""

import yaml
import google.generativeai as genai

def test_gemini_api_basic():
    """Test basic Gemini API functionality"""
    print("🧪 Testing Basic Gemini API")
    print("=" * 50)
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Check API key
        api_key = config['gemini']['api_key']
        print(f"📋 API Key: {api_key[:10]}...{api_key[-10:]}")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(config['gemini']['model'])
        
        print(f"📋 Model: {config['gemini']['model']}")
        
        # Test simple generation
        print("🔄 Testing simple text generation...")
        response = model.generate_content("Hello! Please respond with 'Gemini API is working correctly.'")
        
        if hasattr(response, 'text') and response.text:
            print(f"✅ Gemini API working!")
            print(f"📝 Response: {response.text}")
            return True
        else:
            print(f"❌ No text response received")
            print(f"📊 Response object: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False

def test_gemini_with_mathematical_content():
    """Test Gemini with mathematical content specifically"""
    print("\n🔢 Testing Gemini with Mathematical Content")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        genai.configure(api_key=config['gemini']['api_key'])
        model = genai.GenerativeModel(config['gemini']['model'])
        
        # Test mathematical content
        math_prompt = """
        Please provide a simple mathematical formula for a loss function:
        L = alpha * L1 + beta * L2
        
        Where alpha = 0.5 and beta = 0.3
        """
        
        print("🔄 Testing mathematical content generation...")
        response = model.generate_content(math_prompt)
        
        if hasattr(response, 'text') and response.text:
            print(f"✅ Mathematical content generation working!")
            print(f"📝 Response: {response.text[:200]}...")
            
            # Check for issues
            issues = []
            if "I encountered an issue" in response.text:
                issues.append("Processing error detected")
            if response.text.endswith(",") or response.text.endswith("("):
                issues.append("Incomplete mathematical expression")
            
            if issues:
                print(f"⚠️ Issues found: {issues}")
                return False
            else:
                print(f"✅ No mathematical content issues detected")
                return True
        else:
            print(f"❌ No mathematical response received")
            return False
            
    except Exception as e:
        print(f"❌ Mathematical content test failed: {e}")
        return False

def test_gemini_safety_settings():
    """Test Gemini with different safety settings"""
    print("\n🛡️ Testing Gemini Safety Settings")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        genai.configure(api_key=config['gemini']['api_key'])
        model = genai.GenerativeModel(config['gemini']['model'])
        
        # Test with permissive safety settings
        safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH", 
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_NONE"
            }
        ]
        
        technical_prompt = """
        Provide technical specifications for a neural network architecture:
        - Input layer: 512 dimensions
        - Hidden layers: 2 layers with 256 neurons each
        - Output layer: 10 classes
        - Activation: ReLU for hidden, softmax for output
        """
        
        print("🔄 Testing with permissive safety settings...")
        response = model.generate_content(
            technical_prompt,
            safety_settings=safety_settings
        )
        
        if hasattr(response, 'text') and response.text:
            print(f"✅ Technical content with safety settings working!")
            print(f"📝 Response length: {len(response.text)} characters")
            return True
        else:
            print(f"❌ Technical content blocked or failed")
            if hasattr(response, 'candidates'):
                for candidate in response.candidates:
                    if hasattr(candidate, 'finish_reason'):
                        print(f"📊 Finish reason: {candidate.finish_reason}")
            return False
            
    except Exception as e:
        print(f"❌ Safety settings test failed: {e}")
        return False

def main():
    """Run all Gemini API tests"""
    print("🔬 COMPREHENSIVE GEMINI API TESTING")
    print("=" * 60)
    
    tests = [
        ("Basic Gemini API", test_gemini_api_basic),
        ("Mathematical Content", test_gemini_with_mathematical_content),
        ("Safety Settings", test_gemini_safety_settings)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
            if results[test_name]:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 GEMINI API TEST SUMMARY")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed ({passed/len(tests)*100:.1f}%)")
    
    if passed == len(tests):
        print("🎉 GEMINI API FULLY FUNCTIONAL!")
        return True
    else:
        print("⚠️ GEMINI API HAS ISSUES - May need alternative API")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print(f"\n🔧 GEMINI API ISSUES DETECTED")
        print(f"📋 Possible solutions:")
        print(f"   1. Check API key validity")
        print(f"   2. Verify model name in config.yaml")
        print(f"   3. Test with alternative API key")
        print(f"   4. Consider using OpenRouter as fallback")
    else:
        print(f"\n✅ GEMINI API READY FOR ENHANCED RESEARCH SPIRAL")
