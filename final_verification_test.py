#!/usr/bin/env python3
"""
Final verification before running 100 agents
"""

from multimodal_research_orchestrator import MultimodalResearchOrchestrator
import time

def test_implementation_focused_output():
    """Test that the system produces implementation-focused output"""
    print("🔧 Testing Implementation-Focused Output")
    print("=" * 50)
    
    try:
        orchestrator = MultimodalResearchOrchestrator(silent=False)
        
        # Test with 1 agent from Phase 3 (algorithmic design)
        orchestrator.agents_per_phase = 1
        orchestrator.max_concurrent = 1
        
        print("🔄 Running Phase 3 test (1 agent)...")
        phase_results = orchestrator.run_phase(3)
        
        if phase_results["successful"] > 0:
            print("✅ Phase 3 agent completed successfully")
            
            # Test synthesis
            print("🔄 Testing implementation-focused synthesis...")
            synthesis = orchestrator.synthesize_phase_results(phase_results)
            
            # Check for implementation keywords
            impl_keywords = [
                "implementation", "algorithm", "mathematical", "tensor", 
                "architecture", "code", "pytorch", "loss function", "optimization"
            ]
            
            keyword_count = sum(1 for keyword in impl_keywords if keyword.lower() in synthesis.lower())
            
            print(f"✅ Synthesis complete!")
            print(f"📊 Implementation keywords found: {keyword_count}/{len(impl_keywords)}")
            print(f"📝 Synthesis length: {len(synthesis)} characters")
            print(f"📝 Preview: {synthesis[:300]}...")
            
            if keyword_count >= 5:
                print("✅ Implementation focus: STRONG")
                return True
            else:
                print("⚠️ Implementation focus: WEAK")
                return False
        else:
            print("❌ Phase 3 agent failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_knowledge_base_integration():
    """Quick test of knowledge base integration"""
    print("\n📚 Testing Knowledge Base Integration")
    print("=" * 50)
    
    try:
        from hybrid_agent import HybridAgent
        
        agent = HybridAgent(silent=True)
        
        # Test MMIS paper access
        query = "Use the read_file tool to access the MMIS paper and summarize its key contributions for dataset distillation"
        
        print("🔄 Testing MMIS paper access...")
        response = agent.run(query)
        
        if response and "MMIS" in response and len(response) > 200:
            print("✅ Knowledge base access working")
            print(f"📝 Response preview: {response[:200]}...")
            return True
        else:
            print("❌ Knowledge base access failed")
            return False
            
    except Exception as e:
        print(f"❌ Knowledge base test failed: {e}")
        return False

def main():
    """Run final verification"""
    print("🔬 FINAL VERIFICATION BEFORE 100-AGENT RUN")
    print("=" * 60)
    
    # Test 1: Implementation-focused output
    impl_test = test_implementation_focused_output()
    
    # Test 2: Knowledge base integration
    kb_test = test_knowledge_base_integration()
    
    # Summary
    print(f"\n📊 Final Verification Results:")
    print(f"✅ Implementation Focus: {'PASS' if impl_test else 'FAIL'}")
    print(f"✅ Knowledge Base Access: {'PASS' if kb_test else 'FAIL'}")
    
    if impl_test and kb_test:
        print(f"\n🎉 ALL VERIFICATIONS PASSED!")
        print(f"🚀 System ready for full 100-agent research run")
        print(f"\n📋 To start the full research:")
        print(f"   python run_multimodal_research.py")
        print(f"\n⚠️ Expected duration: 3-4 hours")
        print(f"⚠️ Output: Complete technical implementation specification")
        return True
    else:
        print(f"\n❌ Some verifications failed")
        print(f"🔧 Please fix issues before running full research")
        return False

if __name__ == "__main__":
    main()
