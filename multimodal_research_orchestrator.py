#!/usr/bin/env python3
"""
Specialized Orchestrator for 4-Phase Multimodal Dataset Distillation Research
Implements the comprehensive research directive with 25 agents per phase
"""

import json
import yaml
import time
import threading
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from datetime import datetime
from hybrid_agent import HybridAgent

class MultimodalResearchOrchestrator:
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.silent = silent
        self.total_agents = 100
        self.agents_per_phase = 25
        self.max_concurrent = 2  # Respect API limits
        self.batch_delay = 5  # Seconds between batches
        
        # Progress tracking
        self.progress_status = {}
        self.session_id = None
        
        # Research phases
        self.phases = {
            1: "Comprehensive Analysis of Common Limitations in Multimodal Dataset Distillation",
            2: "Rigorous Assessment of True Data Informativeness (Beyond Inflated Metrics)",
            3: "Novel Algorithmic Design and Calculations for MMIS",
            4: "Verification, Evaluation, and Open-Source Contributions"
        }
        
        if not self.silent:
            print(f"🚀 Multimodal Research Orchestrator initialized")
            print(f"📋 Total agents: {self.total_agents}")
            print(f"📋 Agents per phase: {self.agents_per_phase}")
            print(f"📋 Max concurrent: {self.max_concurrent}")
    
    def set_session_id(self, session_id: str):
        """Set session ID for checkpoint management"""
        self.session_id = session_id
    
    def get_full_research_prompt(self):
        """Return the complete research directive"""
        return """You are well-known ambitious doctor in AI specialized in dataset distillation with very strong background in Math. Your task is followings.

Research Directive: Advancing Multimodal Dataset Distillation for tri modal or more modality datasets

Objective: Develop novel and feasible multimodal dataset distillation (MDD) techniques specifically tailored for the dataset (image, text, audio modalities).As an example you will work with "MMIS (MMIS- Multimodal Dataset for Interior Scene Visual Generation and Recognition) ". However, this technique should be applied to all multimodal datasets. The ultimate goal is to synthesize a compact dataset that maintains comparable performance to models trained on the full original data across various downstream tasks, while rigorously addressing existing limitations and accurately assessing data informativeness.

Phase 1: Comprehensive Analysis of Common Limitations in Multimodal Dataset Distillation
Your initial task is to conduct an exhaustive analysis of the inherent challenges and limitations in current dataset distillation (DD) methodologies, with a particular focus on their applicability and shortcomings in multimodal contexts. This critical review must identify and categorize common impediments that hinder broader adoption and optimal performance of DD in real-world multimodal scenarios.

Specifically, investigate:
• Computational Complexity and Scalability: Examine the bottlenecks associated with the prevalent bi-level optimization frameworks. How do long-range gradient unrolling and repeated model training steps contribute to prohibitive computational costs and memory overhead, especially for high-resolution images or large-scale multimodal datasets?
• Limited Cross-Architecture Generalization: Analyze why synthetic datasets often exhibit poor generalization capabilities across different, unseen model architectures. What are the underlying causes of this architecture overfitting, and how can it be mitigated?
• Modality Collapse and Diversity Issues in Multimodal Data: Critically investigate the phenomenon of "modality collapse" in MDD, where the synthetic data may fail to capture the full diversity and richness of each modality or the intricate cross-modal relationships present in the original dataset. How do existing methods struggle with generating diverse and realistic high-resolution synthetic images? Consider how this extends to text and audio modalities, including the challenge of generating human-readable text.
• Training Instability: Identify sources of instability in distillation optimization, particularly observed in medical imaging dataset distillation, and explore how these impact robustness.
• Bias and Fairness Concerns: Research how the distillation process on imbalanced datasets can exacerbate existing biases, leading to similarly skewed synthetic datasets and potentially unfair model decisions. Analyze if asymmetric supervision across modalities contributes to biased optimization.
• Challenges with Discrete and Structured Data: Explore the specific difficulties of distilling non-image data, such as high-dimensional, sparse, or discrete categorical data (e.g., text, graphs, tabular data). How do current gradient-matching or distribution-matching approaches handle these modalities?

Phase 2: Rigorous Assessment of True Data Informativeness (Beyond Inflated Metrics)
Your second directive is to establish a robust framework for assessing the true data informativeness of distilled multimodal datasets, explicitly decoupling it from confounding factors such as the use of soft labels and data augmentation strategies.

• Deconstructing Soft Label Impact: 
o Investigate the role of soft (probabilistic) labels in distillation. While shown to be crucial for performance, differentiate between their genuine contribution of structured information and mere superficial boosts.
o Explore how "not all soft labels are created equal," emphasizing the need for them to contain meaningful, structured information rather than just smoothed probabilities.
o Examine approaches for generating high-quality soft labels, such as Committee Voting (CV-DD). Consider how these can be adapted for multimodal, instance-level soft labels, encompassing richer annotations like bounding boxes or detailed descriptions. This includes the concept of synthesizing "privileged information" beyond simple data-label pairs.

• Quantifying Informativeness Robustness with DD-Ranking: 
o Utilize and extend the principles of DD-Ranking. Apply its proposed metrics, Label Robust Score (LRS) and Augmentation Robust Score (ARS), to assess the intrinsic quality of distilled multimodal datasets, independent of specific teacher models or evaluation-time augmentations.
o Strive for methods that achieve high LRS and ARS values, indicating that their distilled data's informativeness is less dependent on external performance-boosting techniques.

• Diversity and Realism Metrics: Propose and validate quantitative metrics for synthetic data quality, specifically diversity (e.g., FID for images, distinct n-grams for text) and realism (qualitative assessment for all modalities), ensuring they accurately reflect true data informativeness rather than merely visual appeal.

Phase 3: Novel Algorithmic Design and Calculations for MMIS
Leveraging the insights from the limitation analysis and informativeness assessment, your primary task is to develop novel and feasible MDD techniques for the MMIS dataset (image, text, audio). This involves proposing new algorithms and specifying their underlying calculations.

• Modality-Fusion Dataset Distillation (MFDD) Framework: 
o Core Principle: Focus on instance-level distillation within a unified, semantically rich latent space. This approach aims to abstract modality-specific challenges and integrate them into a common optimization framework, capturing finer-grained details crucial for complex tasks beyond simple classification.
o Multimodal Feature Extraction and Latent Space Mapping (Squeeze Phase): Design a component that maps diverse raw MMIS data (images, text, audio) into a compact latent space using powerful, pre-trained multimodal encoders. For instance, adapting Vision-Language Models (VLMs) for image-text and robust audio-visual backbones for audio-visual data. The rationale is to reduce dimensionality, noise, and enable optimization of continuous latent embeddings for discrete modalities.
o Instance-Level Multimodal Prototype Distillation (Core Distillation Phase): 
	Synthetic Prototype Initialization: Initialize a small set of learnable "synthetic multimodal instance prototypes" in the latent space, significantly smaller than the original dataset.
	Multi-Objective Loss Function: Define and formulate the following critical loss components for optimizing these prototypes: 
	Inter-modal Alignment Loss ($L_{inter_align}$): A contrastive loss (e.g., InfoNCE) applied between corresponding multimodal components of each synthetic instance prototype (e.g., latent image prototype vs. latent text prototype vs. latent audio prototype for the same instance). This ensures cross-modal semantic coherence.
	Intra-modal Instance Diversity Loss ($L_{intra_div}$): A novel contrastive loss within each modality's synthetic instance prototypes. This loss is critical for directly combating "modality collapse" by actively pushing different instance prototypes of the same class away from each other (e.g., distinguishing between different interior scene styles or layouts within the same room type), while ensuring distinct classes are clearly separated.
	Real-to-Synthetic Distribution Matching Loss ($L_{dist_match}$): A distribution matching loss (e.g., Wasserstein distance or Maximum Mean Discrepancy (MMD) with covariance matrix matching) between the distribution of real instance-level embeddings (from the Squeeze Phase) and the synthetic instance prototypes. This ensures the prototypes capture the overall empirical distribution.
	Task-Relevance Guiding Loss ($L_{task_guide}$): Leverage "Task-Specific Proxy" models (e.g., pre-trained object detectors or segmentation models for images, or scene classifiers for text/audio) on the real data. This loss guides the latent prototypes to emphasize features critical for downstream tasks beyond basic classification, such as object detection or semantic segmentation relevant to interior scenes (e.g., furniture, room layout).
	Optimization Strategy: Propose an efficient gradient descent-based optimization of the combined objective ($L_{total} = L_{inter_align} + L_{intra_div} + L_{dist_match} + L_{task_guide}$) in the latent space.
o Instance-Level Multimodal Data Synthesis (Recovery Phase): Formulate a strategy to train/fine-tune a conditional multimodal generative model (e.g., a variant of Stable Diffusion for image-text and potentially adapting to audio generation) that can generate high-resolution image-text-audio samples from the learned latent instance prototypes. This generative model's training should be conditioned on the optimized latent instance prototypes and learned instance-level soft labels (e.g., detailed object descriptions, bounding box coordinates, segmentation masks, or audio event annotations as soft supervision signals).
o Architectural Flexibility: Ensure the proposed techniques are designed for enhanced cross-architecture generalization.

Phase 4: Verification, Evaluation, and Open-Source Contributions
Finally, direct the agents to focus on the practical aspects of research, emphasizing rigorous verification and the importance of open science.

• Experimental Verification and Benchmark Protocols: 
o Define comprehensive benchmark tasks and evaluation protocols for the MMIS dataset: 
	Multimodal Classification: Standard top-1/top-5 accuracy on image-text-audio classification.
	Cross-Modal Retrieval: Evaluate retrieval performance across all modality pairs (e.g., Image-to-Text, Text-to-Image, Audio-to-Image, Image-to-Audio Recall@K) to quantify preservation of cross-modal semantic associations.
	Object Detection and Semantic Segmentation: For the image modality, utilize Mean Average Precision (mAP) and Mean Intersection over Union (mIoU) on interior scene elements, using models trained on the distilled data. This directly evaluates instance-level distillation for complex visual tasks.
	Cross-Architecture Generalization: Rigorously evaluate performance across a diverse set of unseen architectures (e.g., various CNNs, Vision Transformers) to demonstrate the true transferability and robustness of the synthesized dataset.
	Distillation Efficiency: Quantify computational resources (GPU hours, memory footprint) and time required for the entire distillation process, ensuring practical feasibility.
	Synthetic Data Quality (Diversity & Realism): Utilize metrics like FID for images and propose analogous metrics for textual and audio diversity and realism.
	Scalability to IPC: Evaluate performance across a wide range of Images/Instances Per Class (IPC) values, demonstrating sustained effectiveness, particularly at higher IPCs where current methods often struggle.
o Ablation Studies: Insist on thorough ablation studies to demonstrate the individual contribution of each proposed component (inter-modal alignment, intra-modal diversity, task-relevance guiding loss, instance-level synthesis, refined soft labels) to the overall performance.

• Open Code and Reproducibility: Prioritize the use of existing methods with publicly available code for comparative analyses and ensure that all novel algorithms developed contribute to open-source availability for verification and community advancement. Emphasize reproducible experimental setups."""

    def generate_phase_specific_questions(self, phase_number: int, num_agents: int) -> List[str]:
        """Generate specific research questions for each phase"""

        base_prompt = self.get_full_research_prompt()

        phase_focus = {
            1: """Focus specifically on Phase 1: Comprehensive Analysis of Common Limitations in Multimodal Dataset Distillation.
            Your task is to deeply investigate ONE specific limitation area from: computational complexity, cross-architecture generalization,
            modality collapse, training instability, bias concerns, or discrete data challenges. Provide detailed analysis with mathematical
            formulations where applicable.""",

            2: """Focus specifically on Phase 2: Rigorous Assessment of True Data Informativeness.
            Your task is to develop ONE specific aspect of informativeness assessment: soft label impact analysis, DD-Ranking metrics
            implementation, or diversity/realism metrics design. Provide concrete mathematical formulations and evaluation protocols.""",

            3: """Focus specifically on Phase 3: Novel Algorithmic Design and Calculations for MMIS.
            Your task is to design ONE specific component of the MFDD framework: multimodal feature extraction, instance-level prototype
            distillation, loss function formulation, or data synthesis strategy. Provide detailed algorithmic descriptions with mathematical
            calculations.""",

            4: """Focus specifically on Phase 4: Verification, Evaluation, and Open-Source Contributions.
            Your task is to design ONE specific evaluation protocol: benchmark task definition, experimental verification strategy,
            ablation study design, or open-source implementation plan. Provide concrete evaluation metrics and reproducibility guidelines."""
        }

        questions = []
        focus_areas = {
            1: ["computational complexity analysis", "cross-architecture generalization study", "modality collapse investigation",
                "training instability analysis", "bias and fairness assessment", "discrete data challenges"],
            2: ["soft label impact deconstruction", "DD-Ranking metrics implementation", "diversity metrics design",
                "realism assessment protocols", "informativeness robustness evaluation", "augmentation impact analysis"],
            3: ["multimodal feature extraction design", "instance-level prototype initialization", "inter-modal alignment loss formulation",
                "intra-modal diversity loss design", "distribution matching strategy", "task-relevance guiding implementation"],
            4: ["multimodal classification benchmarks", "cross-modal retrieval evaluation", "object detection protocols",
                "cross-architecture testing", "computational efficiency assessment", "open-source implementation"]
        }

        for i in range(num_agents):
            focus_area = focus_areas[phase_number][i % len(focus_areas[phase_number])]
            question = f"""
            {base_prompt}

            {phase_focus[phase_number]}

            Specific Focus Area: {focus_area}

            Agent ID: {i+1} of {num_agents} in Phase {phase_number}

            Provide a comprehensive analysis focusing on your assigned area. Include:
            1. Detailed technical analysis
            2. Mathematical formulations where applicable
            3. Literature review of relevant papers
            4. Novel insights and proposed solutions
            5. Implementation considerations

            Use all available research tools to gather comprehensive information.
            """
            questions.append(question.strip())

        return questions

    def run_single_agent(self, agent_id: int, phase: int, question: str) -> Dict[str, Any]:
        """Run a single agent task"""
        try:
            self.update_agent_progress(agent_id, "PROCESSING...")

            # Create agent
            agent = HybridAgent(silent=True)

            start_time = time.time()
            response = agent.run(question)
            execution_time = time.time() - start_time

            self.update_agent_progress(agent_id, "COMPLETED")

            return {
                "agent_id": agent_id,
                "phase": phase,
                "status": "success",
                "response": response,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.update_agent_progress(agent_id, f"FAILED: {str(e)}")
            return {
                "agent_id": agent_id,
                "phase": phase,
                "status": "error",
                "error": str(e),
                "execution_time": 0,
                "timestamp": datetime.now().isoformat()
            }

    def update_agent_progress(self, agent_id: int, status: str):
        """Update progress status for an agent"""
        self.progress_status[agent_id] = status

    def get_progress_status(self) -> Dict[int, str]:
        """Get current progress status"""
        return self.progress_status.copy()

    def run_phase(self, phase_number: int) -> Dict[str, Any]:
        """Run a single phase with 25 agents"""
        if not self.silent:
            print(f"\n🚀 Starting Phase {phase_number}: {self.phases[phase_number]}")
            print("=" * 80)

        # Generate questions for this phase
        questions = self.generate_phase_specific_questions(phase_number, self.agents_per_phase)

        # Calculate agent IDs for this phase
        start_agent_id = (phase_number - 1) * self.agents_per_phase
        agent_ids = list(range(start_agent_id, start_agent_id + self.agents_per_phase))

        # Initialize progress tracking
        for agent_id in agent_ids:
            self.update_agent_progress(agent_id, "QUEUED")

        # Run agents in batches
        results = []
        batch_size = self.max_concurrent

        for i in range(0, len(agent_ids), batch_size):
            batch_agents = agent_ids[i:i + batch_size]
            batch_questions = questions[i:i + batch_size]

            if not self.silent:
                print(f"\n🔄 Phase {phase_number} - Batch {i//batch_size + 1}: Processing agents {batch_agents}")

            # Run batch concurrently
            with ThreadPoolExecutor(max_workers=batch_size) as executor:
                futures = []
                for j, agent_id in enumerate(batch_agents):
                    future = executor.submit(self.run_single_agent, agent_id, phase_number, batch_questions[j])
                    futures.append(future)

                # Collect results
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)

                    if not self.silent:
                        status = "✅" if result["status"] == "success" else "❌"
                        print(f"  {status} Agent {result['agent_id']} completed ({result['status']}) - {result['execution_time']:.1f}s")

            # Delay between batches (except for last batch)
            if i + batch_size < len(agent_ids):
                if not self.silent:
                    print(f"⏳ Waiting {self.batch_delay}s before next batch...")
                time.sleep(self.batch_delay)

        # Calculate phase statistics
        successful = [r for r in results if r["status"] == "success"]
        failed = [r for r in results if r["status"] == "error"]

        phase_results = {
            "phase": phase_number,
            "phase_name": self.phases[phase_number],
            "total_agents": len(results),
            "successful": len(successful),
            "failed": len(failed),
            "results": results,
            "avg_execution_time": sum(r["execution_time"] for r in successful) / len(successful) if successful else 0
        }

        if not self.silent:
            print(f"\n📊 Phase {phase_number} Complete:")
            print(f"   ✅ Successful: {len(successful)}")
            print(f"   ❌ Failed: {len(failed)}")
            print(f"   ⏱️ Avg time: {phase_results['avg_execution_time']:.1f}s")

        return phase_results

    def synthesize_phase_results(self, phase_results: Dict[str, Any]) -> str:
        """Synthesize results from a single phase"""
        successful_results = [r for r in phase_results["results"] if r["status"] == "success"]

        if not successful_results:
            return f"Phase {phase_results['phase']} failed - no successful agent responses."

        # Create synthesis agent
        synthesis_agent = HybridAgent(silent=True)

        # Build synthesis prompt
        responses_text = ""
        for i, result in enumerate(successful_results, 1):
            responses_text += f"=== AGENT {result['agent_id']} RESPONSE ===\n{result['response']}\n\n"

        synthesis_prompt = f"""
        You are synthesizing research results from Phase {phase_results['phase']}: {phase_results['phase_name']}

        You have {len(successful_results)} agent responses that analyzed different aspects of this phase.

        {responses_text}

        Synthesize these responses into ONE comprehensive analysis that:
        1. Identifies the most important findings and insights
        2. Highlights novel contributions and solutions
        3. Provides mathematical formulations where applicable
        4. Suggests the best implementation approaches
        5. Identifies areas for further investigation

        Focus on creating a coherent, comprehensive summary that captures the best insights from all agents.
        """

        try:
            synthesis = synthesis_agent.run(synthesis_prompt)
            return synthesis
        except Exception as e:
            return f"Synthesis failed for Phase {phase_results['phase']}: {str(e)}"

    def run_complete_research(self) -> Dict[str, Any]:
        """Run the complete 4-phase research orchestration"""
        if not self.silent:
            print("🚀 Starting Complete 4-Phase Multimodal Dataset Distillation Research")
            print("=" * 80)
            print(f"📋 Total agents: {self.total_agents}")
            print(f"📋 Agents per phase: {self.agents_per_phase}")
            print(f"📋 Estimated duration: ~{self.total_agents * 2} minutes")

        start_time = time.time()
        all_phase_results = []
        phase_syntheses = []

        # Run each phase
        for phase_num in range(1, 5):
            phase_results = self.run_phase(phase_num)
            all_phase_results.append(phase_results)

            # Synthesize phase results
            if not self.silent:
                print(f"\n🔄 Synthesizing Phase {phase_num} results...")

            phase_synthesis = self.synthesize_phase_results(phase_results)
            phase_syntheses.append({
                "phase": phase_num,
                "phase_name": self.phases[phase_num],
                "synthesis": phase_synthesis
            })

            if not self.silent:
                print(f"✅ Phase {phase_num} synthesis complete")

        # Final synthesis across all phases
        if not self.silent:
            print(f"\n🔄 Creating final comprehensive synthesis...")

        final_synthesis = self.create_final_synthesis(phase_syntheses)

        total_time = time.time() - start_time

        # Calculate overall statistics
        total_successful = sum(p["successful"] for p in all_phase_results)
        total_failed = sum(p["failed"] for p in all_phase_results)

        final_results = {
            "research_directive": "Advancing Multimodal Dataset Distillation",
            "total_agents": self.total_agents,
            "total_execution_time": total_time,
            "phase_results": all_phase_results,
            "phase_syntheses": phase_syntheses,
            "final_synthesis": final_synthesis,
            "statistics": {
                "total_successful": total_successful,
                "total_failed": total_failed,
                "success_rate": total_successful / self.total_agents * 100,
                "avg_time_per_agent": total_time / self.total_agents
            }
        }

        if not self.silent:
            print(f"\n🎉 Complete Research Orchestration Finished!")
            print(f"⏱️ Total time: {total_time/60:.1f} minutes")
            print(f"📊 Success rate: {final_results['statistics']['success_rate']:.1f}%")
            print(f"✅ Successful agents: {total_successful}/{self.total_agents}")

        return final_results

    def create_final_synthesis(self, phase_syntheses: List[Dict]) -> str:
        """Create final synthesis across all phases"""
        synthesis_agent = HybridAgent(silent=True)

        all_syntheses = ""
        for phase_syn in phase_syntheses:
            all_syntheses += f"=== PHASE {phase_syn['phase']}: {phase_syn['phase_name']} ===\n"
            all_syntheses += f"{phase_syn['synthesis']}\n\n"

        final_prompt = f"""
        You are creating the FINAL COMPREHENSIVE SYNTHESIS for the complete 4-phase research on:
        "Advancing Multimodal Dataset Distillation for tri-modal or more modality datasets"

        Here are the synthesized results from all 4 phases:

        {all_syntheses}

        Create a comprehensive final report that:
        1. Provides an executive summary of the complete research
        2. Highlights the most significant findings from each phase
        3. Presents the novel MFDD (Modality-Fusion Dataset Distillation) framework
        4. Includes mathematical formulations and algorithmic designs
        5. Outlines implementation roadmap and evaluation protocols
        6. Identifies the most promising research directions
        7. Provides concrete next steps for implementation

        This should be a publication-ready comprehensive analysis that advances the field of multimodal dataset distillation.
        """

        try:
            final_synthesis = synthesis_agent.run(final_prompt)
            return final_synthesis
        except Exception as e:
            return f"Final synthesis failed: {str(e)}"
