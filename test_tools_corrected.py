#!/usr/bin/env python3
"""
Corrected comprehensive test of all research tools with proper signatures
"""

import yaml
from tools import discover_tools

def test_individual_tools_corrected(discovered_tools):
    """Test each tool with correct signatures"""
    print("🛠️ Testing Individual Tools (Corrected Signatures)")
    print("=" * 60)
    
    tool_results = {}
    
    for tool_name, tool in discovered_tools.items():
        print(f"\n🔧 Testing {tool_name}...")
        
        try:
            # Test based on correct tool signatures
            if tool_name == "advanced_calculate":
                result = tool.execute(
                    operation="basic_math",
                    expression="2 + 2 * 3"
                )
            elif tool_name == "calculate":
                result = tool.execute(expression="sqrt(16) + log(10)")
            elif tool_name == "critical_evaluation":
                result = tool.execute(
                    content="This is a test research proposal for multimodal learning",
                    evaluation_type="research_quality",
                    criteria=["novelty", "feasibility"]
                )
            elif tool_name == "analyze_feasibility":
                result = tool.execute(
                    research_idea="Develop a new neural network architecture",
                    research_area="machine_learning",
                    assessment_criteria=["technical_complexity", "resource_requirements"]
                )
            elif tool_name == "generate_research_ideas":
                result = tool.execute(
                    domain="machine learning",
                    focus_area="dataset distillation",
                    num_ideas=2
                )
            elif tool_name == "literature_review":
                result = tool.execute(
                    topic="dataset distillation",
                    analysis_type="comprehensive",
                    papers_data=["Sample paper 1", "Sample paper 2"]
                )
            elif tool_name == "read_file":
                result = tool.execute(
                    path="config.yaml"
                )
            elif tool_name == "search_web":
                result = tool.execute(
                    query="machine learning research 2024",
                    max_results=2
                )
            elif tool_name == "search_papers":
                result = tool.execute(
                    query="dataset distillation",
                    action="search",
                    max_results=3
                )
            elif tool_name == "write_file":
                result = tool.execute(
                    path="test_output.txt",
                    content="This is a test file created by the write_file tool."
                )
            elif tool_name == "mark_task_complete":
                result = tool.execute(
                    task_description="Tool testing completed",
                    completion_message="All tools tested successfully"
                )
            else:
                result = {"status": "skipped", "reason": "Unknown tool type"}
            
            if result and (not isinstance(result, dict) or result.get("status") != "error"):
                print(f"   ✅ {tool_name} working")
                tool_results[tool_name] = True
            else:
                print(f"   ❌ {tool_name} failed: {result}")
                tool_results[tool_name] = False
                
        except Exception as e:
            print(f"   ❌ {tool_name} failed: {e}")
            tool_results[tool_name] = False
    
    return tool_results

def test_knowledge_base_access_corrected():
    """Test knowledge base access with correct signatures"""
    print("\n📚 Testing Knowledge Base Access (Corrected)")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        discovered_tools = discover_tools(config, silent=True)
        
        # Test read_file tool with correct signature
        if "read_file" in discovered_tools:
            print("🔄 Testing knowledge base file access...")
            
            # Try to read files with correct parameter name
            test_files = [
                "config.yaml",  # Should definitely exist
                "research_knowledge_base/MMIS- Multimodal Dataset for Interior Scene Visual Generation and Recognition.pdf.md"
            ]
            
            for file_path in test_files:
                try:
                    result = discovered_tools["read_file"].execute(path=file_path)  # Correct parameter name
                    if result and not result.get("error") and len(str(result)) > 100:
                        print(f"   ✅ Successfully read: {file_path}")
                        return True
                    else:
                        print(f"   ⚠️ Could not read {file_path}: {result}")
                except Exception as e:
                    print(f"   ⚠️ Could not read {file_path}: {e}")
            
            print(f"   ❌ Could not read any test files")
            return False
        else:
            print(f"❌ read_file tool not found")
            return False
            
    except Exception as e:
        print(f"❌ Knowledge base access test failed: {e}")
        return False

def test_critical_tools_for_research():
    """Test the most critical tools for research spiral"""
    print("\n🎯 Testing Critical Research Tools")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        discovered_tools = discover_tools(config, silent=True)
        
        critical_tools = {
            "read_file": lambda: discovered_tools["read_file"].execute(path="config.yaml"),
            "write_file": lambda: discovered_tools["write_file"].execute(
                path="test_critical.txt", 
                content="Critical tool test"
            ),
            "advanced_calculate": lambda: discovered_tools["advanced_calculate"].execute(
                operation="basic_math", 
                expression="5 * 3 + 2"
            ),
            "generate_research_ideas": lambda: discovered_tools["generate_research_ideas"].execute(
                domain="AI", 
                focus_area="dataset distillation", 
                num_ideas=1
            ),
            "mark_task_complete": lambda: discovered_tools["mark_task_complete"].execute(
                task_description="Critical tool test", 
                completion_message="Test complete"
            )
        }
        
        results = {}
        for tool_name, test_func in critical_tools.items():
            if tool_name in discovered_tools:
                try:
                    result = test_func()
                    if result and not (isinstance(result, dict) and result.get("error")):
                        print(f"   ✅ {tool_name} working")
                        results[tool_name] = True
                    else:
                        print(f"   ❌ {tool_name} failed: {result}")
                        results[tool_name] = False
                except Exception as e:
                    print(f"   ❌ {tool_name} failed: {e}")
                    results[tool_name] = False
            else:
                print(f"   ❌ {tool_name} not found")
                results[tool_name] = False
        
        success_rate = sum(results.values()) / len(results) * 100
        print(f"\n📊 Critical tools success rate: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Critical tools test failed: {e}")
        return False

def main():
    """Run corrected comprehensive tool testing"""
    print("🔬 CORRECTED COMPREHENSIVE TOOL TESTING")
    print("=" * 70)
    
    # Test 1: Tool Discovery
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        discovered_tools = discover_tools(config, silent=False)
        print(f"✅ Tool discovery successful! Found {len(discovered_tools)} tools")
    except Exception as e:
        print(f"❌ Tool discovery failed: {e}")
        return False
    
    # Test 2: Individual Tools with Corrected Signatures
    tool_results = test_individual_tools_corrected(discovered_tools)
    
    # Test 3: Knowledge Base Access
    kb_success = test_knowledge_base_access_corrected()
    
    # Test 4: Critical Tools
    critical_success = test_critical_tools_for_research()
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 CORRECTED TOOL TEST SUMMARY")
    print("="*70)
    
    working_tools = sum(tool_results.values())
    total_tools = len(tool_results)
    tool_success_rate = working_tools / total_tools * 100 if total_tools > 0 else 0
    
    print(f"🛠️ Individual Tool Results:")
    for tool_name, result in tool_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {tool_name:.<25} {status}")
    
    print(f"\n📚 Knowledge Base Access: {'✅ PASS' if kb_success else '❌ FAIL'}")
    print(f"🎯 Critical Tools: {'✅ PASS' if critical_success else '❌ FAIL'}")
    
    print(f"\n📊 Overall Statistics:")
    print(f"   • Working tools: {working_tools}/{total_tools}")
    print(f"   • Tool success rate: {tool_success_rate:.1f}%")
    print(f"   • Knowledge base access: {'Yes' if kb_success else 'No'}")
    print(f"   • Critical tools working: {'Yes' if critical_success else 'No'}")
    
    # Overall assessment
    overall_success = (
        tool_success_rate >= 70 and
        kb_success and
        critical_success
    )
    
    if overall_success:
        print(f"\n🎉 TOOLS READY FOR ENHANCED RESEARCH SPIRAL!")
        print(f"🚀 All critical functionality working")
        return True
    else:
        print(f"\n⚠️ TOOL ISSUES STILL PRESENT")
        if tool_success_rate < 70:
            print(f"   • Tool success rate too low ({tool_success_rate:.1f}%)")
        if not kb_success:
            print(f"   • Knowledge base access issues")
        if not critical_success:
            print(f"   • Critical tools not working")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ ALL TOOLS FUNCTIONAL WITH CORRECT SIGNATURES")
        print(f"🔄 Enhanced Research Spiral ready for execution")
    else:
        print(f"\n❌ TOOL SIGNATURE ISSUES REMAIN")
        print(f"🔧 Check tool implementations and signatures")
