#!/usr/bin/env python3
"""
Small-Scale Test for Enhanced Make It Heavy Framework

This script tests the enhanced research orchestration system with a small number
of agents (5-10) to verify all components work correctly before scaling to 100 agents.

Usage:
    python small_scale_test.py
"""

import yaml
import time
from orchestrator import TaskOrchestrator
from hybrid_agent import HybridAgent

def load_config():
    """Load configuration from config.yaml"""
    with open('config.yaml', 'r') as f:
        return yaml.safe_load(f)

def test_individual_tools():
    """Test each research tool individually"""
    print("🧪 Testing Individual Research Tools")
    print("=" * 50)
    
    # Create agent with tools
    agent = HybridAgent(silent=True)
    
    # Test 1: Semantic Scholar Search
    print("\n1. Testing Semantic Scholar Search...")
    try:
        result = agent.run("Search for 3 recent papers on dataset distillation using the search_papers tool")
        print("✅ Semantic Scholar search successful")
        print(f"   Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ Semantic Scholar search failed: {e}")
    
    # Test 2: Idea Generation
    print("\n2. Testing Idea Generation...")
    try:
        result = agent.run("Generate 2 moderate innovation research ideas for dataset distillation using the generate_research_ideas tool")
        print("✅ Idea generation successful")
        print(f"   Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ Idea generation failed: {e}")
    
    # Test 3: Feasibility Analysis
    print("\n3. Testing Feasibility Analysis...")
    try:
        result = agent.run("Analyze the feasibility of 'gradient matching for dataset distillation' using the analyze_feasibility tool")
        print("✅ Feasibility analysis successful")
        print(f"   Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ Feasibility analysis failed: {e}")
    
    # Test 4: Critical Evaluation
    print("\n4. Testing Critical Evaluation...")
    try:
        result = agent.run("Critically evaluate the methodology of a dataset distillation study using the critical_evaluation tool")
        print("✅ Critical evaluation successful")
        print(f"   Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ Critical evaluation failed: {e}")
    
    # Test 5: Advanced Calculations
    print("\n5. Testing Advanced Calculations...")
    try:
        result = agent.run("Calculate sample size needed for effect size 0.5 using the advanced_calculate tool")
        print("✅ Advanced calculations successful")
        print(f"   Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ Advanced calculations failed: {e}")
    
    print("\n✅ Individual tool testing complete!")

def test_small_orchestration(num_agents=5):
    """Test small-scale orchestration"""
    print(f"\n🚀 Testing {num_agents}-Agent Orchestration")
    print("=" * 50)
    
    # Load configuration and modify for small test
    config = load_config()
    config['orchestrator']['parallel_agents'] = num_agents
    config['orchestrator']['max_concurrent'] = 2
    config['orchestrator']['batch_delay'] = 2  # Shorter delay for testing
    
    # Create orchestrator
    orchestrator = TaskOrchestrator(silent=False)
    orchestrator.num_agents = num_agents
    orchestrator.max_concurrent = 2
    orchestrator.batch_delay = 2
    
    # Set session ID for testing
    session_id = f"test_{int(time.time())}"
    orchestrator.set_session_id(session_id)
    
    print(f"📋 Configuration:")
    print(f"   - Total agents: {num_agents}")
    print(f"   - Max concurrent: 2")
    print(f"   - Batch delay: 2 seconds")
    print(f"   - Session ID: {session_id}")
    
    # Test query
    test_query = "Analyze dataset distillation techniques for computer vision applications"
    
    print(f"\n🔍 Test Query: {test_query}")
    print(f"⏰ Expected duration: ~{num_agents * 3} minutes")
    
    start_time = time.time()
    
    try:
        # Run orchestration
        results = orchestrator.run_sequential_batch_orchestration(test_query)
        
        execution_time = time.time() - start_time
        
        print(f"\n🎉 Orchestration Complete!")
        print(f"⏱️  Total execution time: {execution_time:.1f} seconds ({execution_time/60:.1f} minutes)")
        print(f"📊 Results summary:")
        print(f"   - Total agents: {results['total_agents']}")
        print(f"   - Successful: {results['statistics']['completed']}")
        print(f"   - Errors: {results['statistics']['errors']}")
        print(f"   - Timeouts: {results['statistics']['timeouts']}")
        print(f"   - Average duration per agent: {results['statistics']['avg_duration']:.1f}s")
        
        # Display synthesis preview
        synthesis = results['final_synthesis']
        print(f"\n📋 Final Synthesis Preview:")
        print(f"   {synthesis[:200]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ Orchestration failed: {e}")
        return None

def test_checkpoint_system():
    """Test checkpoint save/load functionality"""
    print("\n💾 Testing Checkpoint System")
    print("=" * 50)
    
    from orchestrator import CheckpointManager
    
    # Create checkpoint manager
    checkpoint_manager = CheckpointManager()
    
    # Test data
    test_session = f"checkpoint_test_{int(time.time())}"
    test_data = {
        "query": "test query",
        "total_agents": 5,
        "completed_agents": [0, 1, 2],
        "agent_results": [
            {"agent_id": 0, "status": "success", "response": "test response 1"},
            {"agent_id": 1, "status": "success", "response": "test response 2"},
            {"agent_id": 2, "status": "error", "response": "test error"}
        ]
    }
    
    # Test save
    print("1. Testing checkpoint save...")
    try:
        checkpoint_manager.save_checkpoint(test_session, test_data)
        print("✅ Checkpoint save successful")
    except Exception as e:
        print(f"❌ Checkpoint save failed: {e}")
        return False
    
    # Test load
    print("2. Testing checkpoint load...")
    try:
        loaded_data = checkpoint_manager.load_checkpoint(test_session)
        if loaded_data == test_data:
            print("✅ Checkpoint load successful - data matches")
        else:
            print("⚠️ Checkpoint load successful but data differs")
            print(f"   Expected: {len(test_data)} keys")
            print(f"   Loaded: {len(loaded_data)} keys")
    except Exception as e:
        print(f"❌ Checkpoint load failed: {e}")
        return False
    
    # Test list
    print("3. Testing checkpoint listing...")
    try:
        checkpoints = checkpoint_manager.list_checkpoints()
        if test_session in checkpoints:
            print("✅ Checkpoint listing successful - test session found")
        else:
            print("⚠️ Checkpoint listing successful but test session not found")
    except Exception as e:
        print(f"❌ Checkpoint listing failed: {e}")
        return False
    
    print("✅ Checkpoint system testing complete!")
    return True

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🚀 Enhanced Make It Heavy Framework - Comprehensive Test Suite")
    print("=" * 70)
    
    # Test 1: Individual Tools
    test_individual_tools()
    
    # Test 2: Checkpoint System
    checkpoint_success = test_checkpoint_system()
    
    # Test 3: Small Orchestration
    if checkpoint_success:
        orchestration_results = test_small_orchestration(5)
        
        if orchestration_results:
            print("\n🎯 Test Results Summary:")
            print("=" * 30)
            print("✅ Individual tools: PASSED")
            print("✅ Checkpoint system: PASSED")
            print("✅ Small orchestration: PASSED")
            print("\n🚀 System ready for large-scale orchestration!")
            
            # Provide next steps
            print("\n📋 Next Steps:")
            print("1. Scale to 10 agents: test_small_orchestration(10)")
            print("2. Scale to 25 agents: test_small_orchestration(25)")
            print("3. Deploy 100 agents: run_large_scale_research_orchestration()")
            
            return True
        else:
            print("\n❌ Small orchestration failed - check configuration and API keys")
            return False
    else:
        print("\n❌ Checkpoint system failed - check file permissions")
        return False

def main():
    """Main test function"""
    try:
        success = run_comprehensive_test()
        
        if success:
            print("\n🎉 All tests passed! System ready for deployment.")
            exit(0)
        else:
            print("\n❌ Some tests failed. Please check the issues above.")
            exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user.")
        exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        exit(1)

if __name__ == "__main__":
    main()
