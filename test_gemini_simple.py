#!/usr/bin/env python3
"""Simple test for Gemini agent functionality"""

from hybrid_agent import HybridAgent

def test_simple_gemini():
    """Test basic Gemini agent functionality"""
    print("🧪 Testing Gemini Agent")
    print("=" * 50)
    
    try:
        # Create hybrid agent (should use Gemini)
        agent = HybridAgent(silent=False)
        
        print(f"✅ Agent initialized: {agent.get_agent_type()}")
        
        # Test simple query
        print("\n🔍 Testing simple query...")
        response = agent.run("Hello! Please introduce yourself briefly.")
        
        if response:
            print(f"✅ Response received!")
            print(f"📝 Response: {response}")
            return True
        else:
            print("❌ No response received")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_gemini()
    if success:
        print("\n🎉 Gemini agent working!")
    else:
        print("\n❌ Gemini agent failed")
