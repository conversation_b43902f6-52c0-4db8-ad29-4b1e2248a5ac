import yaml
from agent import OpenRouterAgent
from gemini_agent import GeminiAgent

class HybridAgent:
    """
    Hybrid agent that can use either OpenRouter or Gemini based on configuration
    """
    
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.silent = silent
        
        # Determine which agent to use based on available API keys
        self.agent = self._select_agent()
        
        if not self.silent:
            agent_type = "Gemini" if isinstance(self.agent, GeminiAgent) else "OpenRouter"
            print(f"🤖 Using {agent_type} agent")
    
    def _select_agent(self):
        """Select the appropriate agent based on available configuration"""
        
        # Check for Gemini configuration first (prioritize Gemini)
        if ('gemini' in self.config and 
            self.config['gemini'].get('api_key') and 
            self.config['gemini']['api_key'] != 'YOUR_GEMINI_API_KEY_HERE'):
            
            try:
                return GeminiAgent(silent=self.silent)
            except Exception as e:
                if not self.silent:
                    print(f"⚠️ Failed to initialize Gemini agent: {e}")
        
        # Check for OpenRouter configuration as fallback
        if ('openrouter' in self.config and 
            self.config['openrouter'].get('api_key') and 
            self.config['openrouter']['api_key'] not in ['YOUR_OPENROUTER_KEY_HERE', 'YOUR KEY']):
            
            try:
                return OpenRouterAgent(silent=self.silent)
            except Exception as e:
                if not self.silent:
                    print(f"⚠️ Failed to initialize OpenRouter agent: {e}")
        
        # If neither works, raise an error
        raise ValueError(
            "No valid API configuration found. Please set either:\n"
            "1. Gemini API key in config.yaml under 'gemini.api_key'\n"
            "2. OpenRouter API key in config.yaml under 'openrouter.api_key'"
        )
    
    def run(self, user_input: str):
        """Run the selected agent with user input"""
        return self.agent.run(user_input)
    
    def get_agent_type(self):
        """Return the type of agent being used"""
        return "Gemini" if isinstance(self.agent, GeminiAgent) else "OpenRouter"
