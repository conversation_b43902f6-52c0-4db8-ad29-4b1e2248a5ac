#!/usr/bin/env python3
"""
BACKUP: Original Semantic Scholar Tool Implementation

This is a backup of the Semantic Scholar implementation that encountered
rate limiting issues (429 errors). Saved for future use when rate limits
are resolved or for smaller-scale operations.

Rate Limit: 1 request per second
API Key: zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn
"""

import time
import requests
from abc import ABC, abstractmethod

class BaseTool(ABC):
    """Base class for all tools"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Return the name of the tool"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Return a description of what the tool does"""
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> dict:
        """Return the parameters schema for the tool"""
        pass
    
    @abstractmethod
    def execute(self, **kwargs) -> dict:
        """Execute the tool with given parameters"""
        pass

class RateLimiter:
    def __init__(self, calls_per_second=0.8):
        self.calls_per_second = calls_per_second
        self.last_call = 0
    
    def wait(self):
        now = time.time()
        time_since_last = now - self.last_call
        min_interval = 1.0 / self.calls_per_second
        
        if time_since_last < min_interval:
            time.sleep(min_interval - time_since_last)
        
        self.last_call = time.time()

# Global rate limiter for Semantic Scholar API
s2_rate_limiter = RateLimiter(calls_per_second=0.8)

class SemanticScholarTool(BaseTool):
    """Tool for searching academic papers using Semantic Scholar API"""
    
    def __init__(self, api_key="zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn"):
        self.api_key = api_key
        self.base_url = "https://api.semanticscholar.org/graph/v1"
        
    @property
    def name(self) -> str:
        return "search_papers_semantic_scholar"
    
    @property
    def description(self) -> str:
        return "Search for academic papers using Semantic Scholar API (BACKUP VERSION)"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for papers"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results",
                    "default": 10
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020-' for 2020 onwards)"
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, limit: int = 10, year_filter: str = None, **kwargs) -> dict:
        max_retries = 3
        base_delay = 2.0
        
        for attempt in range(max_retries):
            try:
                # Apply rate limiting before each attempt
                s2_rate_limiter.wait()
                
                url = f"{self.base_url}/paper/search"
                params = {
                    "query": query,
                    "limit": limit,
                    "fields": "title,authors,year,abstract,citationCount,url"
                }
                
                if year_filter:
                    params["year"] = year_filter
                
                headers = {"Content-Type": "application/json"}
                if self.api_key and self.api_key != 'YOUR_SEMANTIC_SCHOLAR_API_KEY':
                    headers["x-api-key"] = self.api_key
                
                response = requests.get(url, params=params, headers=headers, timeout=30)
                
                # Handle rate limiting specifically
                if response.status_code == 429:
                    retry_delay = base_delay * (2 ** attempt)
                    print(f"⚠️ Rate limit hit, waiting {retry_delay} seconds before retry {attempt + 1}/{max_retries}")
                    time.sleep(retry_delay)
                    continue
                
                response.raise_for_status()
                
                data = response.json()
                papers = data.get('data', [])
                
                return {
                    "status": "success",
                    "papers": papers,
                    "total_found": len(papers),
                    "query": query,
                    "attempts": attempt + 1,
                    "source": "Semantic Scholar (BACKUP)"
                }
                
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    return {
                        "status": "error",
                        "error": f"Request failed after {max_retries} attempts: {str(e)}",
                        "query": query,
                        "source": "Semantic Scholar (BACKUP)"
                    }
                else:
                    retry_delay = base_delay * (2 ** attempt)
                    print(f"⚠️ Request failed, retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
            except Exception as e:
                return {
                    "status": "error",
                    "error": str(e),
                    "query": query,
                    "source": "Semantic Scholar (BACKUP)"
                }
        
        return {
            "status": "error",
            "error": "Max retries exceeded",
            "query": query,
            "source": "Semantic Scholar (BACKUP)"
        }

# Example usage
if __name__ == "__main__":
    tool = SemanticScholarTool()
    result = tool.execute("dataset distillation", limit=5)
    print(f"Result: {result}")
