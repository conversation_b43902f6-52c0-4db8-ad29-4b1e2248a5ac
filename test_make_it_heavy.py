#!/usr/bin/env python3
"""Test make_it_heavy.py with Gemini agent"""

import sys
import time
from orchestrator import TaskOrchestrator

def test_orchestrator_initialization():
    """Test if orchestrator initializes properly with Gemini"""
    print("🧪 Testing Orchestrator Initialization")
    print("=" * 50)
    
    try:
        # Create orchestrator
        orchestrator = TaskOrchestrator(silent=False)
        
        print(f"✅ Orchestrator initialized successfully!")
        print(f"📋 Number of agents: {orchestrator.num_agents}")
        print(f"📋 Max concurrent: {orchestrator.max_concurrent}")
        print(f"📋 Batch delay: {orchestrator.batch_delay}")
        
        return orchestrator
        
    except Exception as e:
        print(f"❌ Orchestrator initialization failed: {e}")
        return None

def test_small_orchestration():
    """Test small orchestration with 2 agents"""
    print("\n🚀 Testing Small Orchestration (2 agents)")
    print("=" * 50)
    
    try:
        # Create orchestrator with small configuration
        orchestrator = TaskOrchestrator(silent=False)
        orchestrator.num_agents = 2
        orchestrator.max_concurrent = 1
        orchestrator.batch_delay = 1
        
        # Set session ID
        session_id = f"test_small_{int(time.time())}"
        orchestrator.set_session_id(session_id)
        
        print(f"📋 Configuration:")
        print(f"   - Total agents: 2")
        print(f"   - Max concurrent: 1")
        print(f"   - Session ID: {session_id}")
        
        # Test query
        test_query = "What are the main benefits of dataset distillation in machine learning?"
        
        print(f"\n🔍 Test Query: {test_query}")
        print(f"⏰ Expected duration: ~2-3 minutes")
        
        start_time = time.time()
        
        # Run orchestration
        results = orchestrator.run_sequential_batch_orchestration(test_query)
        
        execution_time = time.time() - start_time
        
        print(f"\n🎉 Small Orchestration Complete!")
        print(f"⏱️  Total execution time: {execution_time:.1f} seconds ({execution_time/60:.1f} minutes)")
        print(f"📊 Results summary:")
        print(f"   - Total agents: {results['total_agents']}")
        print(f"   - Successful: {results['statistics']['completed']}")
        print(f"   - Errors: {results['statistics']['errors']}")
        
        # Display synthesis preview
        synthesis = results['final_synthesis']
        print(f"\n📋 Final Synthesis Preview:")
        print(f"   {synthesis[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Small orchestration failed: {e}")
        return False

def main():
    """Test make_it_heavy functionality"""
    print("🚀 Enhanced Make It Heavy Framework - Deployment Test")
    print("=" * 70)
    
    # Test 1: Orchestrator Initialization
    orchestrator = test_orchestrator_initialization()
    
    if not orchestrator:
        print("\n❌ Cannot proceed - orchestrator initialization failed")
        return False
    
    # Test 2: Small Orchestration
    small_test_success = test_small_orchestration()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"✅ Orchestrator Init: {'PASSED' if orchestrator else 'FAILED'}")
    print(f"✅ Small Orchestration: {'PASSED' if small_test_success else 'FAILED'}")
    
    if orchestrator and small_test_success:
        print("\n🎉 All tests passed! System ready for full deployment.")
        print("\n📋 Ready for:")
        print("1. Full 100-agent deployment: python make_it_heavy.py")
        print("2. Interactive mode with custom queries")
        return True
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
