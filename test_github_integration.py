#!/usr/bin/env python3
"""Test GitHub integration with your token"""

import yaml
from github_research_tool import GitHubResearchTool

def test_github_integration():
    """Test GitHub API with your token"""
    print("🔧 Testing GitHub Integration")
    print("=" * 50)
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        github_token = config['github']['token']
        print(f"📋 GitHub Token: {github_token[:10]}...{github_token[-10:]}")
        
        # Test GitHub tool
        github_tool = GitHubResearchTool(github_token=github_token)
        
        # Test search
        result = github_tool.execute(
            query="dataset distillation",
            search_type="repositories",
            max_results=3
        )
        
        if result and 'repositories' in result:
            print(f"✅ GitHub integration successful!")
            print(f"📊 Found {len(result['repositories'])} repositories")
            for repo in result['repositories'][:2]:
                print(f"   - {repo['name']}: {repo['description'][:100]}...")
            return True
        else:
            print("❌ GitHub integration failed - no results")
            return False
            
    except Exception as e:
        print(f"❌ GitHub integration failed: {e}")
        return False

if __name__ == "__main__":
    test_github_integration()
