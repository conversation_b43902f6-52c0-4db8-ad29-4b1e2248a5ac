Image /page/0/Picture/4 description: The image displays the text "Semantic Scholar API" next to a logo. The logo consists of several lines of varying lengths and colors, predominantly blue and yellow, arranged in a fan-like or arrow-like pattern pointing downwards and to the left.

# Semantic Scholar API

Semantic Scholar API - Tutorial

Image /page/0/Picture/7 description: A dark blue rectangle with rounded corners, representing a computer screen or window, is shown. Inside this rectangle, a yellow wrench is depicted at an angle. Two light blue circles, resembling buttons or icons, are positioned in the top left corner of the rectangle. The entire scene is set against a light yellow circular background. Surrounding the yellow circle and the blue rectangle are scattered blue and light blue squares, some with a textured or pixelated appearance, creating a digital or abstract effect.

## Learn to search for papers and authors, download datasets, and more

#### [Introduction](#page-0-0)

What is an API?

The [Semantic](#page-1-0) Scholar APIs

How to make requests faster and more efficiently

[Example:](#page-2-0) Request paper details

## Make Calls to the Semantic Scholar API

Step 1: [Keyword](#page-5-1) search for relevant papers Step 2: Get [recommended](#page-9-0)

## Introduction

<span id="page-0-0"></span>The Semantic Scholar REST API uses standard HTTP verbs, response codes, and au tutorial will teach you how to interact with the API by sending requests and analyzing All code examples are shown in Python. If you prefer a code-free experience, follow a Semantic Scholar Postman [Collection](https://www.postman.com/science-operator-43364886/workspace/semantic-scholar-examples/collection/37460422-e99f1d74-d11c-48c8-93a8-f33ec0e0aea1), which lets you test out the API on Postman, and API testing platform.

# What is an Application Programming Interface (A

An API is a structured way for applications to communicate with each other. Applica API requests to one another, for instance to retrieve data.

Each API request consists of:

- An API endpoint, which is the URL that requests are sent to. The URL consists c URL and the specific endpoint's resource path (See Figure 1).
- A request method, such as GET or POST. This is sent in the HTTP request and t type of action to perform.

#### papers

Step 3: Look up [authors](#page-9-0)

## Additional Resources

**[Pagination](#page-14-1)** 

**Examples** using search query [parameters](#page-14-2)

How to [download](#page-15-0) full datasets

How to update datasets with [incremental](#page-18-0) diffs

Tips for working with [downloaded](#page-19-0) datasets

# **Base URL**

**Resource path** 

#### https://api.semanticscholar.org/graph/v1/paper/search

Figure 1. The endpoint for Semantic Scholar's paper [relevance](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_relevance_search) search endpoint.

Each API request may also include:

- Query parameters, which are appended to the end of the URL, after the resource
- A request header, which may contain information about the API key being used.
- A request body, which contains data being sent to the API.

After the request is sent, the API will return a response. The response includes a stat indicating whether the request was successful and any requested data. The respons include requested data.

Common status codes are:

- 200, OK. The request was successful.
- 400, Bad Request. The server could not understand your request. Check your p
- 401, Unauthorized. You're not authenticated or your credentials are invalid.
- 403, Forbidden. The server understood the request but refused it. You don't have access the requested resource.
- 404, Not Found. The requested resource or endpoint does not exist.
- 429, Too Many Requests. You've hit the rate limit, slow down your requests.
- 500, Internal Server Error. Something went wrong on the server's side.

# The Semantic Scholar APIs

Semantic Scholar contains three APIs, each with its own unique base URL:

- <span id="page-1-0"></span>Academic Graph API returns details about papers, paper authors, paper citation Base URL: <https://api.semanticscholar.org/graph/v1>
- Recommendations API recommends papers based on other papers you give it. <https://api.semanticscholar.org/recommendations/v1>
- Datasets API lets you download Semantic Scholar's datasets onto your local ma host the data yourself and do custom queries. Base URL: [https://api.semanticscholar.org/datasets/v1](https://api.semanticscholar.org/datasets/v1/)

See the Semantic Scholar API [documentation](https://api.semanticscholar.org/api-docs/) for more information about each API: endpoints. The documentation describes how to correctly format requests and pars each endpoint.

# How to make requests faster and more efficiently

<span id="page-1-1"></span>Heavy use of the API can cause a slowdown for everyone. Here are some tips to avc ceilings and slowdowns when making requests:

- Use an API Key. Users without API keys are affected by the traffic from all other users, who share a single API key. But using an individual API key automatically request per second rate across all endpoints. In some cases, users may be gran higher rate following a review. Learn more about API keys and how to request o
- Use batch endpoints. Some endpoints have a corresponding batch or bulk end more results in a single response. Examples include the paper [relevance](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_relevance_search) search paper bulk [search\)](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search) and the paper details [endpoint](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_get_paper) (batch version: [paper](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/post_graph_get_papers) batch e requesting large quantities of data, use the bulk or batch versions whenever pose
- Limit "fields" parameters. Most endpoints in the API contain the "fields" query portain allows users to specify what data they want returned in the response. Avoid inc than you need, because that can slow down the response rate.
- Download Semantic Scholar Datasets. When you need a request rate that is high provided by API keys, you can download Semantic Scholar's datasets and run q [Datasets](https://api.semanticscholar.org/api-docs/datasets) API provides endpoints for easily downloading and maintaining Sema datasets. See the How to [Download](#page-15-0) Full Datasets section of the tutorial under  $\lambda$ Resources for more details.

# Example: Request paper details (using Python)

<span id="page-2-0"></span>Now we'll make a request to the paper details [endpoint](https://api.semanticscholar.org/api-docs/graph#tag/Paper-Data/operation/get_graph_get_paper) by running Python code. Cor listed under Prerequisites below before proceeding. If you prefer to follow along in P request in Postman is located [here](https://www.postman.com/science-operator-43364886/semantic-scholar-examples/request/nvkscgu/details-about-a-paper). For more examples of API requests using Pytho Make Calls to the [Semantic](#page-5-0) Scholar API.

#### Prerequisites:

- Install Python if it is not already on your machine.
- Install pip, Python's package manager, if it is not already on your machine.

According to the Academic Graph API documentation, the paper details endpoint is and its resource path is /paper/{paper\_id}.

### Details about a paper

Examples:

- https://api.semanticscholar.org/graph/v1/ paper/649def34f8be52c8b66281af98ae884c09aef38b o Returns a paper with its paperId and title.
- https://api.semanticscholar.org/graph/v1/ paper/649def34f8be52c8b66281af98ae884c09aef38b? fields=url, year, authors
  - o Returns the paper's paperId, url, year, and list of authors. **Each author has authorld and name.**
- https://api.semanticscholar.org/graph/v1/ paper/649def34f8be52c8b66281af98ae884c09aef38b? fields=citations.authors

Image /page/2/Picture/16 description: The image shows a section of a web page displaying API documentation. At the top, a GET request is highlighted with a red border, showing the endpoint "/paper/{paperId}". Below this, under the heading "Response samples", there are buttons for status codes 200 and 400. Further down, the content type is specified as "application/json", followed by a JSON code snippet that includes "paperId", "corpusId", and "externalIds".

Figure 2. Each endpoint's resource path is listed in the API documentation.

When combined with the Academic Graph base URL, the endpoint's URL is: [https://api.semanticscholar.org/graph/v1/paper/{paper\\_id}](https://api.semanticscholar.org/graph/v1/paper/%7Bpaper_id)

The curly brackets in the resource path indicate that **paper\_id** is a path parameter, w by a value when the request is sent. Accepted formats for the value of paper\_id are Path Parameters section of the documentation.

PATH PARAMETERS

| paper id | string                                                                                                          |
|----------|-----------------------------------------------------------------------------------------------------------------|
| required | The following types of IDs are supported:                                                                       |
|          |                                                                                                                 |
|          | $\bullet$ $\leq$ sha> - a Semantic Scholar ID, e.g.                                                             |
|          | 649def34f8be52c8b66281af98ae884c09aef38b                                                                        |
|          | CorpusId: <id> - a Semantic Scholar numerical ID, e.g.<br/><math display="inline">\bullet</math></id>           |
|          | 215416146                                                                                                       |
|          | DOI: <doi> - a Digital Object Identifier, e.g.   DOI: 10.18653/<br/><math display="inline">\bullet</math></doi> |
|          | N18-3011                                                                                                        |
|          | ARXIV: <id> - arXiv.rg, e.g. ARXIV: 2106.15928</id>                                                             |
|          | MAG: <id> - Microsoft Academic Graph, e.g. MAG: 11221823</id>                                                   |
|          | $\overline{ACL}\cdot\overline{C}$ - Association for Computational Linquistics e g                               |
|          |                                                                                                                 |

Figure 3. Accepted formats are listed in the Path Parameters section.

The Query Parameters section of the documentation only lists a single optional para fields parameter takes a string of comma-separated field names, which tell the API which to return in the response.

#### Responses

#### 200 Paper with default or requested fields

RESPONSE SCHEMA: application/json

| paperId     | string<br>Semantic Scholar's primary unique identifier for a paper.                                                                                                                              |
|-------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| corpusId    | integer<br>Semantic Scholar's secondary unique identifier for a paper.                                                                                                                           |
| externalIds | object<br>An object that contains the paper's unique identifiers in<br>external sources. The external sources are limited to: ArXiv,<br>MAG, ACL, PubMed, Medline, PubMedCentral, DBLP, and DOI. |
| url         | string<br>URL of the paper on the Semantic Scholar website.                                                                                                                                      |
| title       | string<br>Title of the paper.                                                                                                                                                                    |

Figure 4. Fields that can be returned in the response are listed in the Response Sche Responses.

For our Python request, we'll query the same paper ID given in the documentation's  $\epsilon$ request the paper's title, the year of publication, the abstract, and the citationCount

```
import requests
paperId = "649def34f8be52c8b66281af98ae884c09aef38b"
# Define the API endpoint URL
url = f"http://api.semanticscholar.org/graph/v1/paper/{paperId}"
# Define the query parameters
query_params = {"fields": "title,year,abstract,citationCount"}
# Directly define the API key (Reminder: Securely handle API keys :
api_key = "your api key goes here" # Replace with the actual API k
# Define headers with API key
headers = \{ "x-\text{api}-\text{key}" : \text{api}\_\text{key} \}# Send the API request
response = requests.get(url, params=query_params, headers=headers)
# Check response status
if response.status code == 200:
   response_data = response.json()
    # Process and print the response data as needed
    print(response_data)
else:
    print(f"Request failed with status code {response.status_code}:
```

Note that this request is using an API key. The use of API keys is optional but recom more about API keys and how to get one [here.](https://www.semanticscholar.org/product/api#api-key)

We are using the Python Requests library to send the request. So we know the [respo](https://www.w3schools.com/python/ref_requests_response.asp) [property](https://www.w3schools.com/python/ref_requests_response.asp) named status\_code that returns the response status. We check the status. print the successfully returned data or the error message.

See the API documentation for how the response is formatted. Each Status Code se with further details about the response data that is returned.

```
Responses
    > 200 Paper with default or requested fields
    > 400 Bad query parameters
    \vee 404 Bad paper id
   RESPONSE SCHEMA: application/json
   \rightarrow error
                      string
                      Depending on the case, error message may be any of these:
                        • "Paper/Author/Object not found"
                        . "Paper/Author/Object with id ### not found"
Figure 5. The Responses section describes how responses are formatted.
When the request is successful, the JSON object returned in the response is:
 {
       "paperId": "649def34f8be52c8b66281af98ae884c09aef38b",
      "title": "Construction of the Literature Graph in Semantic Scho
      "abstract": "We describe a deployed scalable system for organi:
       "year": 2018,
       "citationCount": 365
 }
Semantic Scholar API section for more Python examples u
search, paper recommendations, and authors endpoints.
```

## Make Calls to the Semantic Scholar API

Use Case: Let's suppose you are an early-career academic researcher interested i in generative AI. You would like to learn about recent research developments in th field and discover what areas are most exciting for future research.

<span id="page-5-0"></span>How to use Semantic Scholar: You can do a keyword search for relevant papers on  $\epsilon$ can pick out a few papers that seem the most interesting, then recommend more part similar to them. You can examine the list of recommended papers to see which are the and which authors worked on them, then look up other research by those same auth

Let's walk through those scenarios together. We're going to use Python, but you can using the Postman [collection](https://www.postman.com/science-operator-43364886/semantic-scholar-examples/collection/g4giumx/getting-started-with-semantic-scholar-api).

<span id="page-5-1"></span>

# Step 1: Keyword search for relevant papers

#### Use Case: We want to learn more about generative AI, so we'll start by searching f generative AI research papers.

Two [Academic](https://api.semanticscholar.org/api-docs/graph) Graph API endpoints use Semantic Scholar's [custom-trained](https://blog.allenai.org/building-a-better-search-engine-for-semantic-scholar-ea23a0b661e7) ranker 1 keyword searches: the paper [relevance](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_relevance_search) search endpoint and the paper bulk [search](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search) e

Paper bulk search should be used in most cases because paper relevance search is intensive. The paper relevance search endpoint is able to return more detailed inform paper's authors, its referenced papers, and the papers that cite it. The paper bulk sea sorting and special syntax in the query parameter. In this scenario, we will use the  $p_i$ endpoint.

# Get the Endpoint URL

The Academic Graph API endpoint's [base](https://www.semanticscholar.org/product/api/tutorial#start) URL is: http://api.semanticscholar.org/gra

Whenever we want to retrieve data from or send data to an endpoint in the Academi how the URL starts. The API [documentation](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search) for paper bulk search endpoint lists its i /paper/search/bulk, so the endpoint's full URL is:

http://api.semanticscholar.org/graph/v1/paper/search/bulk

### Set the Query Parameters

The paper bulk search API documentation lists the following query parameters:

- **query** sets the search term
- **token** automatically handles pagination
- fields determines what data the API endpoint will return to you
- sort allows users to sort the results by the paperId, publicationDate, or citationC
- **publicationTypes** filters results by paper publication type (e.g. journal articles)
- openAccessPdf filters results by whether they contain public PDFs of papers
- minCitationCount filters results by whether they have at least a given number c
- publicationDateOrYear filters results by a date range
- $\bullet$  year filters results by a year range
- venue filters results by publication venue
- fieldsOfStudy filters results by the paper's field of study

Only the first query parameter, **query**, is required in every request. The **token** query p included in the original request. Instead, it is returned in the response to the original included in subsequent requests to automatically handle [pagination.](#page-14-1)

In our request, we will include 3 query parameters: **query, fields**, and **year**:

- Use quotation marks in the query to search for the phrase "generative AI". See t section for more examples of using [search](#page-14-2) query syntax.
- In fields, include the title, url, type of publication, date of publication, and link to paper. Separate field names with commas, without spaces. See the API docume available field names.
- Filter for papers published during or after the year 2023 by using the "2023–" sy

These query parameters are appended to the end of the URL, so the complete URL  $\mathbb{I}$ http://api.semanticscholar.org/graph/v1/paper/search/bulk?query="generative

ai"&fields=title,url,publicationTypes,publicationDate,openAccessPdf&year=2023-

### Send the Request

The URL is long and hard to read, so in our code we'll break it up a bit:

```
import requests
import json
# Specify the search term
query = "generative ai"# Define the API endpoint URL
url = "http://api.semanticscholar.org/graph/v1/paper/search/bulk"
# Define the query parameters
query_params = \{ "query": '"generative ai"',
     "fields": "title,url,publicationTypes,publicationDate,openAcces
     "year": "2023-"
}
# Directly define the API key (Reminder: Securely handle API keys :
api_key = "your api key goes here" # Replace with the actual API \vdash# Define headers with API key
headers = \{ "x-\text{api}-\text{key}" : \text{api}\_\text{key} \}# Send the API request
response = requests.get(url, params=query_params, headers=headers)
```

The request is formatted and sent to the API endpoint, and the response is captured response.

According to the API documentation, if the request was successful, with status code response variable contains three fields:

| Paper Data                                                | $\vee$ | Responses                       |                                                                                                                    |
|-----------------------------------------------------------|--------|---------------------------------|--------------------------------------------------------------------------------------------------------------------|
| <b>GET</b><br>Suggest paper query<br>completions          |        |                                 | $\vee$ 200 Batch of papers with default or requested fields                                                        |
| <b>POST</b><br>Get details for multiple<br>papers at once |        | RESPONSE SCHEMA:                | application/json                                                                                                   |
| <b>GET</b><br>Paper relevance search                      |        | $\rightarrow$ total<br>required | string<br>Approximate number of matching search results                                                            |
| <b>GET</b><br>Paper bulk search                           |        | $\rightarrow$ token             | string<br>A continuation token that must be provided to fetc<br>results. Present only when more results can be fet |
| <b>GET</b><br>Details about a paper                       |        | $\rightarrow$ data $\rangle$    | Array of objects (Contents of this page)                                                                           |
| <b>GET</b><br>Details about a paper's<br>authors          |        | $\vee$ 400 Bad query parameters |                                                                                                                    |
| <b>GET</b><br>Details about a paper's<br>citations        |        | RESPONSE SCHEMA:                | application/json                                                                                                   |
| <b>GET</b><br>Details about a paper's<br>references       |        | $\rightarrow$ error             | string<br>Depending on the case, error message may be any                                                          |

Figure 6. The API documentation lists the data format of the response schema.

The **total** parameter is an estimate of how many papers were found that matched the the **token** parameter is used for [pagination,](https://www.semanticscholar.org/product/api/tutorial#pagination) and the **data** parameter contains the dat the endpoint. Note that the paper bulk search endpoint's use of tokens to handle pag the paper relevance search endpoint's use of the **offset** and *limit* query parameters f

The next part of our code saves the data returned from the endpoint to a json file titl and prints the code's progress to the console. If the **token** parameter is present, fetc of responses.

```
print(f"Will retrieve an estimated {response['total']} documents")
retrieved = \theta# Write results to json file and get next batch of results
with open(f"papers.json", "a") as file:
     while True:
         if "data" in response:
            retrieved += len(response["data"])
             print(f"Retrieved {retrieved} papers...")
            for paper in response["data"]:
                 print(json.dumps(paper), file=file)
        # checks for continuation token to get next batch of resuli
         if "token" not in response:
             break
         response = requests.get(f"{url}&token={response['token']}")
print(f"Done! Retrieved {retrieved} papers total")
```

Each data object in the **papers.json** file contains the fields we requested, as well as

```
{
     "paperId": "001720a782840652b573bb4794774aee826510ca",
    "url": "https://www.semanticscholar.org/paper/001720a782840652l
     "title": "Developing Design Features to Facilitate AI-Assisted 
     "openAccessPdf": null,
     "publicationTypes": null,
     "publicationDate": "2024-05-03"
}
{
     "paperId": "0019e876188f781fdca0c0ed3bca39d0c70c2ad2",
    "url": "https://www.semanticscholar.org/paper/0019e876188f781fd
    "title": "Artificial intelligence prompt engineering as a new d
     "openAccessPdf": {
         "url": "https://eber.uek.krakow.pl/index.php/eber/article/v
         "status": "GOLD"
     },
     "publicationTypes": [
         "JournalArticle",
         "Review"
     ],
     "publicationDate": null
}
```

See the S2folks GitHub code examples for [another](https://github.com/allenai/s2-folks/tree/main/examples/python/search_bulk) version of this call.

# Step 2: Get recommended papers

<span id="page-9-0"></span>Use Case: In this section, we want to get a list of recommended papers based on seed papers. We're interested in the most cited papers, so we'll request information recommended papers' citation counts.

Two [Recommendations](https://api.semanticscholar.org/api-docs/recommendations) API endpoints can recommend papers: one gives recomme on a [single](https://api.semanticscholar.org/api-docs/recommendations#tag/Paper-Recommendations/operation/get_papers_for_paper) seed paper from the user, while the other [takes](https://api.semanticscholar.org/api-docs/recommendations#tag/Paper-Recommendations/operation/post_papers) a list of positive seed paper negative seed papers from the user. Both endpoints return an array of papers in desirelevance.

We will use the endpoint that takes two lists of positive and negative seed papers, w http://api.semanticscholar.org/recommendations/v1/papers

This is a POST request, so we need to check the endpoint's Request Body Schema s documentation, to see the format in which the data must be sent in the body of the r

Image /page/10/Picture/0 description: The image displays a UI for paper recommendations. On the left side, there are two options: 'POST Get recommended papers for lists of positive and negative example papers' and 'GET Get recommended papers for a single positive example paper'. On the right side, under 'REQUEST BODY SCHEMA: application/json', it lists 'positivePaperIds' as an 'Array of strings' and 'negativePaperIds' as an 'Array of strings'. Below this, under 'Responses', it shows '> 200 List of recommendations with default'.

Figure 7. The Request Body Schema section describes how to format the data in a P

The positive and negative seed paperIds need to be sent as two arrays, **positivePap** negativePaperIds.

For **positivePaperIds**, we'll use two positive seed papers:

- Human-Centred AI in Education in the Age of Generative AI Tools, paperId 02138d6d094d1e7511c157f0b1a3dd4e5b20ebee
- Responsible Adoption of Generative AI in Higher Education: Developing <sup>a</sup> "Point Approach Based on Faculty Perspectives, paperId 018f58247a20ec6b3256fd3119f57980a6f37748

For **negativePaperIds**, we'll use one negative seed paper:

A Novel Generative AI-Based Framework for Anomaly Detection in Multicast Me Grid Communications, paperId 0045ad0c1e14a4d1f4b011c92eb36b8df63d65L

In our request to this API endpoint, we provide the following query parameters:

- $\bullet$  The **fields** query parameter, with the **citationCount** field, which returns how much paper is cited by other papers. We won't include the *influentialCitationCount* fie field keeps track of how often the paper has a big influence on other papers.
- The limit query parameter, which limits the number of recommended papers ret this to the max value of 500.

In a new Python script, the request is formatted and sent to the API endpoint.

```
import requests
import json
# Define the API endpoint URL
url = "https://api.semanticscholar.org/recommendations/v1/papers"
# Define the query parameters
query_params = {
     "fields": "title,url,citationCount,authors",
     "limit": "500"
}
```

```
# Define the request data
data = f "positivePaperIds": [
         "02138d6d094d1e7511c157f0b1a3dd4e5b20ebee", 
         "018f58247a20ec6b3256fd3119f57980a6f37748"
    \frac{1}{\sqrt{2}} "negativePaperIds": [
         "0045ad0c1e14a4d1f4b011c92eb36b8df63d65bc"
     ]
}
# Directly define the API key (Reminder: Securely handle API keys :
api_key = "your api key goes here" # Replace with the actual API \vdash# Define headers with API key
headers = \{ "x-\text{api}-\text{key}" : \text{api}-\text{key} \}# Send the API request
response = request, post(url, parameters)=query\_params, json=data, head(# Sort the recommended papers by citation count
papers = response["recommendedPapers"]
papers.sort(key=lambda paper: paper["citationCount"], reverse=True]
with open('recommended_papers_sorted.json', 'w') as output:
     json.dump(papers, output)
```

A successful request returns a response with the **recommendedPapers** parameter. see the most cited papers, the papers are sorted by the **citationCount** parameter, the written to a JSON file, recommended\_papers\_sorted.json:

```
\sqrt{2} {
         "paperId": "833ff07d2d1be9be7b12e88487d5631c141a2e95",
         "url": "https://www.semanticscholar.org/paper/833ff07d2d1be
        "title": "Teacher Professional Development on Self-Determin
         "citationCount": 24,
         "authors": [
             {
                 "authorId": "2281351310",
                "name": "Thomas K. F. Chiu"
             },
\{ "authorId": "2281342663",
                 "name": "C. Chai"
             },
             {
                 "authorId": "2300302076",
```

```
"name": "P. J. Williams"
            },
\{ "authorId": "2300141520",
                "name": "Tzung-Jin Lin"
 }
        ]
    },
    {
        "paperId": "144b8d9c10ea111598aa239100cd6ed5c6137b1c",
       "url": "https://www.semanticscholar.org/paper/144b8d9c10ea1
        "title": "Artificial intelligence as part of future practic
        "citationCount": 19,
        "authors": [
            {
                "authorId": "2300748516",
                "name": "Anna Jaruga-Rozdolska"
 }
       \Box },
```

The recommended papers are now sorted in descending order of citation count, wit citations at the top of the list.

See the S2folks GitHub for an [example](https://github.com/allenai/s2-folks/tree/main/examples/python/find_and_recommend_papers) of using the recommendations endpoint that seed paper.

# Step 3: Look up authors

#### Use Case: We want to get more information about the authors of the highest cited

<span id="page-12-0"></span>The batch authors [endpoint](https://api.semanticscholar.org/api-docs/graph#tag/Author-Data/operation/post_graph_get_authors) in Academic Graph API can return information about mu https://api.semanticscholar.org/graph/v1/author/batch.

This endpoint is a POST, and it accepts an array of **authorids** called ids in the reques includes **authorId**s of the four authors of the most cited paper:

- 2281351310
- 2281342663
- 2300302076
- 2300141520

The only query parameter accepted by the endpoint is **fields**, where we can request information about the authors, including:

- The author name
- The url of the author's page on Semantic Scholar
- Their number of papers in Semantic Scholar, called paperCount
- The author's **hindex**, a measure of their research impact

• An array of all **papers** by the author in Semantic Scholar

In a new Python script, the request is sent to the API endpoint.

```
import requests
 import json
 # Define the API endpoint URL
 url = "https://api.semanticscholar.org/graph/v1/author/batch"
 # Define the query parameters
 query_params = {
      "fields": "name,url,paperCount,hIndex,papers"
 }
 # Define the request data
 data = { "ids": ["2281351310","2281342663","2300302076","2300141520"]
 }
 # Directly define the API key (Reminder: Securely handle API keys :
 api_key = "your api key goes here" # Replace with the actual API \vdash# Define headers with API key
 headers = \{ "x-\text{api}-\text{key}" : \text{api}-\text{key} \}# Send the API request
 response = requests.post(url, params=query_params, json=data, heade
 # Save the results to json file
 with open('author_information.json', 'w') as output:
       json.dump(response, output)
The successful request returns an array of objects that contain author information.
  \lceil {
           "authorId": "2281351310",
           "url": "https://www.semanticscholar.org/author/2281351310",
          "name": "Thomas K. F. Chiu",
           "paperCount": 2,
           "hIndex": 1,
           "papers": [
               {
                    "paperId": "630642b7040a0c396967e4dab93cf73094fa4f8
                   "title": "An experiential learning approach to lear
               },
               {
                   "paperId": "833ff07d2d1be9be7b12e88487d5631c141a2e9
```

"title": "Teacher Professional Development on Self-

See the S2folks GitHub for other [interesting](https://github.com/allenai/s2-folks/tree/main/examples/python/find_coauthored_papers) examples of using the author endpoints

<span id="page-14-0"></span>

### Additional Resources

<span id="page-14-1"></span>

### Pagination

 } ]

},

Pagination is a technique used in APIs to manage and retrieve large sets of data in s manageable chunks. This is particularly useful when dealing with extensive datasets efficiency and reduce the load on both the client and server.

Some Semantic Scholar endpoints, like paper [relevance](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_relevance_search) search, require the use of th parameters to handle pagination:

- **Limit:** Specifies the maximum number of items (e.g., papers) to be returned in  $\varepsilon$ response. For example, in the request [https://api.semanticscholar.org/graph/v1](https://api.semanticscholar.org/graph/v1/paper/search?query=halloween&limit=3) guery=halloween&limit=3, the limit=3 indicates that the response should includ papers.
- Offset: Represents the starting point from which the API should begin fetching skip a certain number of items. For example, if **offset=10**, the API will start retri the 11th item onward.

Other endpoints, like paper bulk [search](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search), require the use of the **token** parameter to ha

• Token: A "next" token or identifier provided in the response, pointing to the next allows fetching the next page of results.

In either case, the client requests the API for the first page of results. The API respor number of items. If there are more items to retrieve, the client can use the offset par token in subsequent requests to get the next page of results until all items are fetche pagination allows clients to retrieve large datasets efficiently, page by page, based o

### Examples using search query parameters

Semantic Scholar's paper bulk [search](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search) supports a variety of operators that enable ad and precise specifications in search queries. All keywords in the search query are  $m_i$ words in the paper's **title** and **abstract**. Refer to the API [Documentation](https://api.semanticscholar.org/api-docs/#tag/Paper-Data/operation/get_graph_paper_bulk_search) for all suppo Below are examples of varying complexity to help you get started.

<span id="page-14-2"></span>Example 1.

```
((cloud computing) | virtualization) +security -privacy
```

Matches papers containing the words "cloud" and "computing", OR the word "virtuali or abstract. The paper title or abstract must also include the term "security" but shou word "privacy". For example, a paper with the title "Ensuring Security in Cloud Compu Environments" could be included, unless its abstract contains the word "privacy".

```
Example 2.
```

```
"red blood cell" + artificial intelligence
```

Matches papers where the title or abstract contains the exact phrase "red blood cell" words "artificial" and "intelligence". For example, a paper with the title "Applications c Intelligence in Healthcare" would be included if it also contained the phrase "red bloc abstract.

```
Example 3.
```

```
fish*
```

Matches papers where the title or abstract contains words with "fish" in their prefix,  $s$ "fishes", or "fishy". For example a paper with the title "Ecology of Deep-Sea Fishes" w

```
Example 4.
```

```
bugs~3
```

Matches papers where the title or abstract contains words with an edit distance of 3 "bugs", such as "buggy", "but", "buns", "busg", etc. An edit is the addition, removal, or c character.

#### Example 5.

"blue lake"  $~3$ 

Matches papers where the title or abstract contains phrases with up to 3 terms betw specified in the phrase. For example, a paper titled "Preserving blue lakes during the abstract containing a phrase such as "blue fishes in the lake" would be included.

# How to download full datasets

Semantic Scholar datasets contain data on papers, authors, abstracts, embeddings, Datasets are grouped by releases, and each release is a snapshot of the datasets at release date. Make requests to the **[Datasets](https://api.semanticscholar.org/api-docs/datasets) API** to see the list of available release d datasets contained in a given release, and to download links to datasets.

<span id="page-15-0"></span>All Semantic Scholar datasets are delivered in JSON format.

## Step 1: See all release dates

Use the list of [available](https://api.semanticscholar.org/api-docs/datasets#tag/Release-Data/operation/get_releases) releases endpoint to see all dataset release dates.

```
import requests
```

```
# Define base URL for datasets API
base_url = "https://api.semanticscholar.org/datasets/v1/release/"
```

# To get the list of available releases make a request to the base response = requests.get(base\_url)

```
# Print the response data
print(response.json())
```

The response is a list of release dates, which contain all releases through the date th made:

Image /page/16/Figure/6 description: The image displays a list of dates in a code-like format, indicating a range of available releases. The earliest available release is dated "2022-05-10", and the latest available release is dated "2023-10-31". The list includes several dates in between, with an ellipsis (...) suggesting that there are more dates not shown in full.

### Step 2: See all datasets for a given release date

Use the list of [datasets](https://api.semanticscholar.org/api-docs/datasets#tag/Release-Data/operation/get_release) in a release endpoint to see all datasets contained in a given endpoint takes the **release\_id**, which is simply the release date, as a query paramete can also be set to "latest" instead of the actual date value to retrieve datasets from t

```
import requests
base url = "https://api.semanticscholar.org/datasets/v1/release/"
# Set the release id
release_id = "2023-10-31"
```

# Make a request to get datasets available the latest release response = requests.get(base url + release id)

# Print the response data

print(response.json())

Image /page/17/Picture/3 description: The image displays a diagram illustrating the structure of a data release. On the left side, text boxes explain the contents of the response. The top box states that the response will contain a release ID, a README with licensing and usage information, and a list of datasets. The bottom box clarifies that each dataset object will include the dataset name, a brief description, and a README with documentation and attribution data. Arrows point from these text boxes to corresponding sections of a JSON-like code structure on the right. This code structure shows a "release\_id" and a "README" for the overall release, followed by a "datasets" array. Each element in the "datasets" array is an object with "name", "description", and "README" fields, detailing different datasets such as "abstracts", "authors", "citations", "embeddings-specter\_v1", "papers", "publication-venues", "s2orc", and "tldrs".

#### Step 3: Get download links for datasets

Use the [download](https://api.semanticscholar.org/api-docs/datasets#tag/Release-Data/operation/get_dataset) links for a dataset endpoint to get download links for a specific da release date. This step requires the use of a Semantic Scholar API key.

```
import requests
base url = "https://api.semanticscholar.org/datasets/v1/release/"
# This endpoint requires authentication via api key
api_{k}ey = "your api key goes here"
headers = \{ "x-\text{api}-\text{key}" : \text{api~key} \}# Set the release id
release_id = "2023-10-31"
# Define dataset name you want to download
dataset name = 'papers'
# Send the GET request and store the response in a variable
response = requests.get(base_url + release_id + '/\text{dataset}/' + dataset
```

```
# Process and print the response data
print(response.json())
```

The response contains the dataset name, description, a README with license and u and temporary, pre-signed download links for the dataset files:

| {                                                                                                        |
|----------------------------------------------------------------------------------------------------------|
| "name": "papers",                                                                                        |
| "description": "The core attributes of a paper (title, authors, date, etc.).<br>200M records in 30       |
| "README": "Semantic Scholar Academic Graph Datasets<br><br>The \"papers\" dataset provides core metadata |
| "files": [                                                                                               |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_035bc98c        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_06eb4d79        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_0cf3df4/        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_0eebe944        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_19040349        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_31ef62b1        |
| "https://ai2-s2ag.s3.amazonaws.com/staging/2023-10-31/papers/20231103_070507_00032_9g8gj_3eaa3f79        |

<span id="page-18-0"></span>

# How to update datasets with incremental diffs

The [incremental](https://api.semanticscholar.org/api-docs/datasets#tag/Incremental-Updates/operation/get_diff) diffs endpoint in the Datasets API allows users to get a comprehen changes—or "diffs"—between any two releases. Full datasets can be updated from o another to avoid downloading and processing data that hasn't changed. This endpoi use of a Semantic Scholar API key.

This endpoint returns a list of all the "diffs" required to catch a given dataset up from date to the end release date, with each "diff" object containing only the changes from the next sequential release.

Each "diff" object itself contains two lists of files: an "update files" list and a "delete fi in the "update files" list need to be inserted or replaced by their primary key. Records files" list should be removed from your dataset.

```
import requests
# Set the path parameters
start_release_id = "2023-10-31"
end_release_id = "2023-11-14"
dataset_name = "author"# Set the API key. For best practice, store and retrieve API keys v
api_{\text{key}} = "your api key goes here"headers = \{ "x-\text{api}-\text{key}": \text{api}\_\text{key} \}# Construct the complete endpoint URL with the path parameters
url = f"https://api.semanticscholar.org/datasets/v1/diffs/{start_re
# Make the API request
response = requests.get(url, headers=headers)
```

Image /page/19/Figure/0 description: The image displays a Python code snippet that extracts and prints differences from a response. The code first assigns the value of the 'diffs' key from a JSON response to a variable named 'diffs'. It then prints the content of the 'diffs' variable. The printed output shows a dictionary with keys 'dataset', 'start\_release', 'end\_release', and 'diffs'. The 'diffs' key contains a list of dictionaries, each representing a release difference. These dictionaries include 'from\_release', 'to\_release', 'update\_files', and 'delete\_files'. The 'update\_files' and 'delete\_files' keys contain lists of URLs pointing to files on Amazon S3.

# Tips for working with downloaded datasets

<span id="page-19-0"></span>Explore the following sections for inspiration on leveraging your downloaded data. P that the tools, libraries, and frameworks mentioned below are not a comprehensive l performance will vary based on the size of your data and machine's capabilities. The tools with no affiliation to Semantic Scholar, and are simply offered as suggestions t initial exploration of our data.

### Command line tools

Perhaps the simplest way to view your downloaded data is via the command line thi like more and tools like [jq.](https://jqlang.github.io/jq/)

#### 1. The more command

You can use the **more** command without installing any external tool or library. This c to display the contents of a file in a paginated manner and lets you page through the downloaded file in chunks without loading up the entire dataset. It shows one screen and allows you to navigate through the file using the **spacebar** (move forward one set (move forward one line) commands.

**Example:** You downloaded the papers dataset, and renamed the file to "papersDatas" papersDataset" command to view the file:

Image /page/20/Picture/0 description: The image displays a dark background with white text, resembling a code snippet or data output. The text appears to be in JSON format, containing entries with keys like "corpusid", "externalids", "title", "authors", "year", "url", and "publicationdate". There are two main entries visible, each starting with a curly brace and containing various data points related to academic papers or research articles. The first entry seems to describe a paper with the title "DESIGN-ST STRUCTURAL AND SAFETY ASPECT" published in 1989. The second entry describes a paper with the title "Carbon C".

#### 2. The jq tool

 $jq$  is a lightweight and flexible command-line tool for exploring and manipulating JSC you can easily view formatted json output, select and view specific fields, filter data conditions, and more.

**Example**: You downloaded the papers dataset, and renamed the file to "papersDatas"<br>command to format output is jq ',' <file-name>, so use the jq , papersDat to view the formatted file:

```
$ jq . PapersDataset
  "corpusid": 106452074,
  "externalids": {
    "ACL": null,
    "DBLP": null,
    "ArXiv": null,
    "MAG": "594909919",
    "CorpusId": "106452074",
    "PubMed": null,
    "DOI": null,
    "PubMedCentral": null
  },
  "url": "https://www.semanticscholar.org/paper/9c67c3d99a6c37b0cb66a51c838f18e167991f49",
  "title": "DESIGN--THE LATEST STRUCTURAL AND SAFETY ASPECT",
  "authors": [
    €
      "authorId": "95068330",
      "name": "B. Rapo"
    }
  ь
  "venue": "",
  "publicationvenueid": null,
  "year": 1989,
  "referencecount": 0,
  "citationcount": 0,
  "influentialcitationcount": 0,
  "isopenaccess": false,
  "s2fieldsofstudy": [
    Ł.
      "category": "Political Science",
      "source": "s2-fos-model"
    },
    €
      "category": "Engineering",
      "source": "external"
    }
  ь
  "publicationtypes": null,
  "publicationdate": "1989-05-04",
   journal": null
  "corpusid": 85822462,
  "externalids": {
    "ACL": null,
```

**Example:** You want to filter publication venues that are only journals. You can use *jq*<br>objects by a condition with the command **ig ' . I select(has("type") and .t** objects by a condition with the comr<br>"journal")' publicationVenues

```
$ jq '. | select(has("type") and .type == "journal")' publicationVenues
  "id": "9030933b-681f-4637-b6da-fdf564f98094",
  "alternate_issns": [],
  "alternate names": [
    "J Inn Mong Univ Technol"
  ь
  "alternate_urls": [],
  "issn": null,
  "name": "Journal of Inner Mongolia University of Technology",<br>"type": "journal",
  "url": nullК
  "id": "6660aa04-ef17-4011-9d67-f8a234c43fa9",
  "alternate_issns": [],
  "alternate_names": [
    "Int J Electron Secur Digit Forensics"
  Ъ
  "alternate_urls": [],
  "issn": "1751-911X",
  "name": "International Journal of Electronic Security and Digital Forensics",
  "type": "journal",
  "url": "http://www.inderscience.com/browse/index.php?journalID=217"
  "id": "0772b421-5aaf-4d5d-8eeb-2e898266f356",
  "alternate_issns": [],
   'alternate_names": [
    "Essay Crit'
  Ъ
  "alternate_urls": [],
  "issn": "0014-0856",
  ISSN 8014-0650 ,<br>"name": "Essays in Criticism",
  "type": "journal",
  "url": "https://academic.oup.com/eic/issue"
```

### Python Pandas library

[Pandas](https://pandas.pydata.org/docs/index.html) is a powerful and easy-to-use data analysis and manipulation library availab Using Pandas, you can effortlessly import, clean, and explore your data. One of the k Pandas is a [DataFrame,](https://pandas.pydata.org/docs/user_guide/dsintro.html#dataframe) which can be thought of as a table of information, akin to a rows and columns. Each column has a name, similar to a header in Excel, and each set of related data. With a DataFrame, tasks like sorting, filtering, and analyzing your straightforward. Now we will see how to leverage basic Pandas functions to view an Semantic Scholar data in a DataFrame.

**Example:** The **[head](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.head.html#pandas-dataframe-head)** function. In Pandas you can use the *head()* function to view the your dataframe.

```
import pandas as pd
```

```
# Read JSON file into Pandas DataFrame. The 'lines' parameter indic
df = pd.read json('publication venues dataset', lines=True)
```

```
# Print the first few rows of the DataFrame
print(df.head())
```

The output is below. You will notice that this is a very wide dataframe, where each  $\alpha$ a field in our json object (e.g. id, name, issn, url, etc.). By default pandas only shows that columns. To view all the columns, you can configure the pandas display settings bef output, with pd.set\_option('display.max\_columns', None)

|   | id                                   |                                     |
|---|--------------------------------------|-------------------------------------|
| 0 | a909b7ee-0540-4cbf-8a08-5c1792f46c09 | https://www.degruyter               |
| 1 | 9030933b-681f-4637-b6da-fdf564f98094 | ...                                 |
| 2 | ee31da77-e1ff-418c-848d-e704f9c57118 | ...                                 |
| 3 | 6660aa04-ef17-4011-9d67-f8a234c43fa9 | http://www.inderscience.com/browse/ |
| 4 | 0772b421-5aaf-4d5d-8eeb-2e898266f356 | https://academic.oup                |

**Example:** The **[count](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.count.html#pandas-dataframe-count)** function. We can use the  $count()$  function to count the number data in them (e.g. not null). This can be useful to test the quality of your dataset.

# Display count of non-null values for each column print(df.count())

Output:

| id              | 19259 |
|-----------------|-------|
| alternate_issns | 19259 |
| alternate_names | 19259 |
| alternate_urls  | 19259 |
| issn            | 17904 |
| name            | 19259 |
| type            | 10384 |
| url             | 11241 |
| dtype:          | int64 |

**Example:** Filtering. We can filter our data by specifying conditions. For example, let's loaded our authors' dataset into a dataframe, and want to filter by authors who have papers and been cited at least 10 times. After applying this filter, let's select and disp authorid, name, papercount, and citationcount fields.

```
#filter dataframe by authors who have more than 5 publications and 
df = df[(df.papercount >= 5) \& (df.citationcount >= 10)]
```

```
# Select and print a subset of the columns in our filtered datafram
print(df[['authorid', 'name', 'papercount', 'citationcount']])
```

Output:

Image /page/22/Picture/11 description: The image displays a table with columns for authorid, name, papercount, and citationcount. The first row shows authorid 72212190 with the name G. J. Rao, a papercount of 33, and a citationcount of 195. The second row shows authorid 92218388 with the name N. Timchenko, a papercount of 32, and a citationcount of 68. An ellipsis indicates that there are more rows in the table.

**Example:** Sorting. Pandas offers a variety of sorting functions to organize our data. below, we use the *[sort\\_values\(](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.sort_values.html#pandas-dataframe-sort-values))* function to sort the dataframe by the "name" colum the *authorid* and *name* columns. The default is ascending order, so in this case our o authors in alphabetical order. e can filter our data by specifying conditions. For exal we have loaded our authors' dataset into a dataframe, and want to filter by authors v at least 5 papers and been cited at least 10 times. After applying this filter, let's selec the authorid, name, papercount, and citationcount fields.

```
#Let's sort our authors in alphabetical order
df = df.sort_values(by='name')
```

Output:

|   | authorid   | name         |
|---|------------|--------------|
| 3 | 133999031  | G. G. Conde  |
| 0 | 72212190   | G. J. Rao    |
| 1 | 92218388   | N. Timchenko |
| 2 | 2060916559 | 豊村 浩一        |

**Example:** Check for missing values. Let's say we want to assess the quality of our dationfor missing (null) values. We can count how many missing values we have by using [sum\(\)](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.sum.html#pandas-dataframe-sum) functions.

# Count and print the number of missing values for each author att print(df.isnull().sum())

Output:

| authorid      | 0 |
|---------------|---|
| externalids   | 4 |
| url           | 0 |
| name          | 0 |
| aliases       | 1 |
| affiliations  | 4 |
| homepage      | 4 |
| papercount    | 0 |
| citationcount | 0 |
| hindex        | 0 |
| dtype: int64  |   |

### Apache Spark (Python examples)

[Apache](https://spark.apache.org/) Spark is a fast and powerful processing engine that can analyze large-scale traditional methods via in-memory caching and optimized query execution. Spark of variety of programming languages, so you can utilize its capabilities regardless of th are coding in. In our examples we will showcase the Spark [Python](https://spark.apache.org/docs/latest/api/python/index.html) API, commonly kn

**Example:** The **[show](https://spark.apache.org/docs/latest/api/python/reference/pyspark.sql/api/pyspark.sql.DataFrame.show.html#pyspark-sql-dataframe-show)** function. PySpark's show() function is similar to print() or head will display the first few rows of data. Let's load up our *publication venues* data into a DataFrame and see how it looks:

from pyspark.sql import SparkSession

```
# Create a Spark session
spark = SparkSession.builder.appName("dataset_exploration").getOrC
```

# Read the dataset file named 'publication venues dataset' into a I  $df = spark.read.join("publication venues dataset")$ 

# Display the first few rows df.show()

Output:

| alternate_issns | alternate_names       | alternate_urls                         | id        | issn                 | name | t |
|-----------------|-----------------------|----------------------------------------|-----------|----------------------|------|---|
| []              | [Przegląd Nar]        | [http://www.ip.uz...a909b7ee-0540-4cb] | 2084-848X | Przegląd Narodowo... | N    |   |
| []              | [J Inn Mong Univ ...] | [http://www.ip.uz...9030933b-681f-463] | NULL      | Journal of Inner ... | J    |   |
| []              | [Spot]                | [http://www.ip.uz...ee31da77-e1ff-418] | 1049-0450 | Spot                 | N    |   |
| []              | [Int J Electron S...] | [http://www.ip.uz...6660aa04-ef17-401] | 1751-911X | International Jou... | J    |   |
| []              | [Essay Crit]          | [http://www.ip.uz...0772b421-5aaf-4d5] | 0014-0856 | Essays in Criticism  | J    |   |
| []              | [Rev divulg inter...] | [http://siaiweb06...de15cd98-213f-4d9] | 2318-9290 | Revista de divulg... | N    |   |
| []              | [Vopr prakt pediatr]  | [http://siaiweb06...8bd02a00-8437-4ef] | 1817-7646 | Voprosy praktičes... | N    |   |
| []              | [Al-ma mun J Kaji...] | [http://siaiweb06...946e1aa8-b7f4-443] | 2746-0509 | Al-Ma mun Jurnal     | N    |   |
| []              | [TANE]                | [http://siaiweb06...df9252cb-f21c-49a] | 0496-8026 | TANE                 | N    |   |
| [2450-0054]     | [Comput Sci Math ...] | [http://ur6sa6pd6...5c59b008-b4f3-423] | 1508-4183 | Computer Science     | N    |   |
| []              | [The School Admin...] | [http://ur6sa6pd6...c60c51f4-e5d7-4c0] | 0036-6439 | School Administrator | J    |   |
| []              | [Acta math Spalat...] | [https://doi.org/..85029f86-412a-4b6]  | 2623-5803 | Acta mathematica     | N    |   |
| []              | [Int J Res Stud S...] | [https://doi.org/..aacd8a4f-9d63-45a]  | 2349-4751 | International Jou... | J    |   |
| []              | [Future Technol]      | [https://doi.org/..269bd730-c70f-47e]  | 2832-0379 | Future Technology    | N    |   |
| []              | [Acta orthop Iugosl]  | [https://doi.org/..5b4ee56a-8f8d-4ac]  | 0350-2309 | Acta orthopaedica    | N    |   |
| []              | [Const Leg Acad S...] | [https://doi.org/..840564cb-c335-407]  | 2663-5399 | Constitutional Le... | J    |   |
| []              | [Novye issled]        | [https://doi.org/..e408cf8a-fba5-499]  | 2072-8840 | Novye issledovania   | J    |   |
| []              | [Grain Process]       | [https://doi.org/..c455ed59-45d6-4fb]  | NULL      | Grain Processing     | J    |   |
| []              | [Sorbonne stud la...] | [http://www.sorbo...50f91ce0-74c2-4a8] | 2647-4867 | Sorbonne student     | N    |   |
| []              | [J Wound Care]        | [https://info.jou...92aaef6b-439e-433] | 0969-0700 | Journal of Wound     | J    |   |

**Example:** The **[printSchema](https://spark.apache.org/docs/latest/api/python/reference/pyspark.sql/api/pyspark.sql.DataFrame.printSchema.html#pyspark-sql-dataframe-printschema)** function. PySpark offers a handy *printSchema()* functio explore the structure of your data

# Display the object schema

```
df.printSchema()
```

Output:

Image /page/24/Figure/10 description: The image displays a hierarchical structure of data fields, likely representing a schema or data model. The root element contains several fields: 'alternate\_issns', 'alternate\_names', and 'alternate\_urls', all of which are arrays that can contain null elements. Additionally, there are fields for 'id', 'issn', 'name', 'type', and 'url', all of which are strings that can be null. The structure is presented in a clear, indented format, typical of documentation or code inspection tools.

**Example:** Summary statistics. PySpark offers a handy *describe*(*)* function to delve in summary statistics for the specified columns in our dataset. In this example we des

papercount, *citationcount*, and orderBy attributes of our author data. In the results w average papercount of authors in this dataset, along with their average *citationcoun* other common statistical measures.

```
df.describe(["papercount", "citationcount", "hindex"]).show()
```

#### Output:

| summary | papercount         | citationcount     | hindex             |
|---------|--------------------|-------------------|--------------------|
| count   | 32654              | 32654             | 32654              |
| mean    | 17.75              | 66.0              | 2.75               |
| stddev  | 17.114808402861737 | 91.69878225291035 | 2.7537852736430506 |
| min     | 1                  | 0                 | 0                  |
| max     | 33                 | 195               | 6                  |

**Example:** Sorting. We can call the  $\text{orderBy}()$  function and specify the column we wa this case papercount. We also call the desc() function to sort in descending order (fr lowest papercount). We also only want to display the *authorid, name, and papercoul* display the top 3 records.

```
df = df.orderBy(col("papercount").desc())
df.select("authorid", "name", "papercount").show(3)
```

#### Output:

| authorid                | name         | papercount |
|-------------------------|--------------|------------|
| 72212190                | G. J. Rao    | 33         |
| 92218388                | N. Timchenko | 32         |
| 133999031               | G. G. Conde  | 5          |
| only showing top 3 rows |              |            |

#### MongoDB

[MongoDB](https://www.mongodb.com/) is a fast and flexible database tool built for exploring and analyzing large  $s$ Think of it as a robust digital warehouse where you can efficiently organize, store, ar volumes of data. In addition, MongoDB is a NoSQL database that stores data in a fle format, scales horizontally, supports various data models, and is optimized for perforMongoDB offers both [on-premise](https://www.mongodb.com/try/download/community) and fully managed cloud options ([Atlas\)](https://www.mongodb.com/atlas) and can b the Mongo shell or a GUI (known as Mongo [Compass](https://www.mongodb.com/products/tools/compass)). You can check out our guide [Mongo](https://docs.google.com/document/d/1Ej5vCd-LZiOxo03b0D7XwgFKQt82v9yz2LaBgWrQQvE/edit#heading=h.2pwe9hxzxy1m) if you need help getting started. In the example below, we have imported a  $p$ a Mongo Atlas cluster and show you how to leverage the Mongo Compass GUI to vie your data.

Once you have imported your data, you can view it via Compass as shown in the exapted can leverage the Compass [documentation](https://www.mongodb.com/docs/compass/current/) to discover all its capabilities. We have lis items on the user interface to get you acquainted:

- Data can be viewed in the default list view (shown below), object view, or table v the button on the upper right hand corner. In the list view, each 'card' displays a this case a paper object. Notice that MongoDB appends its own ID, known as  $O$ record.
- You can filter and analyze your data using the filter pane at the top of the screen Explain button to see how your filters were applied to obtain your result set. Not Mongo is a NoSQL database, it has a slightly different query language from SQL filtering and manipulation.
- The default tab is the *Documents* tab where you can view and scroll through yo also switch to the *Aggregations tab* to transform, filter, group, and perform aggr on your dataset. In the *Schema* tab, Mongo provides an analysis of the schema When you click on the Indexes tab, you will find that the default index for search ObjectId. If you believe you will perform frequent searches using another attribu you can add an additional index to optimize performance.
- You can always add more data to your dataset via the green Add Data button rig query bar

| Filter<br>Type a query: { field: 'value' }                                      |          |
|---------------------------------------------------------------------------------|----------|
| Tell Compass what documents to find (e.g. which movies were released in 2000)   |          |
| <b>ADD DATA +</b><br>EXPORT DATA -                                              | $1 - 20$ |
| _id: ObjectId('657ba6fe6f087ca1991e0927')                                       |          |
| corpusid: 79233401                                                              |          |
| * externalids: Object                                                           |          |
| url: "https://www.semanticscholar.org/paper/10e498c4399eda3fac2599d3487b541a"   |          |
| title: "AB0588 Risk Factors for Thromboembolic Events in Patients with Idiopat" |          |
| * authors: Array (9)                                                            |          |
| venue: ""<br>publicationvenueid: null                                           |          |
| year: 2016                                                                      |          |
| referencecount: 0                                                               |          |
| citationcount: 0                                                                |          |
| influentialcitationcount: 0<br>isopenaccess: false                              |          |
| > s2fieldsofstudy: Array (2)                                                    |          |
| publicationtypes: null                                                          |          |
| publicationdate: "2016-06-01"                                                   |          |
| > journal: Object                                                               |          |
| _id: ObjectId('657ba6fe6f087ca1991e0923')                                       |          |
| corpusid: 106452074                                                             |          |
| * externalids: Object                                                           |          |
| url: "https://www.semanticscholar.org/paper/9c67c3d99a6c37b0cb66a51c838f18e1"   |          |
| title: "DESIGN--THE LATEST STRUCTURAL AND SAFETY ASPECT"                        |          |
| > authors: Array (1)                                                            |          |
| venue: ""<br>publicationvenueid: null                                           |          |
| year: 1989                                                                      |          |
| referencecount: 0                                                               |          |
| citationcount: 0                                                                |          |
| influentialcitationcount: 0<br>isopenaccess: false                              |          |
| > s2fieldsofstudy: Array (2)                                                    |          |
| publicationtypes: null                                                          |          |
| publicationdate: "1989-05-04"                                                   |          |

#### Setting Up MongoDB

You have the option of installing MongoDB onto your machine, or using their manag service option on the cloud, otherwise known as [Atlas.](https://www.mongodb.com/atlas) Once you set up your databa download the GUI tool (Mongo [Compass\)](https://www.mongodb.com/products/tools/compass) and connect it to your database to visually your data. If you are new to mongo and want to just explore, you can setup a free clu just a few easy steps:

#### Set Up a Free Cluster on MongoDB Atlas:

- 1. Sign Up/Login:
  - 1.1. Visit the MongoDB Atlas website.

- 1.2. Sign up for a new account or log in if you already have one.
- 2. Create a New Cluster:
  - 2.1. After logging in, click on "Build a Cluster."
  - 2.2. Choose the free tier (M0) or another desired plan.
  - 2.3. Select your preferred cloud provider and region.
- 3. Configure Cluster:
  - 3.1. Set up additional configurations, such as cluster name and cluster tier.
  - 3.2. Click "Create Cluster" to initiate the cluster deployment. It may take a few m

#### Connect to MongoDB Compass:

- 1. Download and Install MongoDB Compass:
  - 1.1. Download MongoDB Compass from the official website.
  - 1.2. Install the Compass application on your computer.
- 2. Retrieve Connection String:
  - 2.1. In MongoDB Atlas, go to the "Clusters" section.
  - 2.2. Click on "Connect" for your cluster.
  - 2.3. Choose "Connect Your Application."
  - 2.4. Copy the connection string.
- 3. Connect Compass to Atlas:
  - 3.1. Open MongoDB Compass.
  - 3.2. Paste the connection string in the connection dialog.
  - 3.3. Modify the username, password, and database name if needed.
  - 3.4. Click "Connect."

#### Import Data:

- 1. Create a Database and Collection:
  - 1.1. In MongoDB Compass, navigate to the "Database" tab.
  - 1.2. Create a new database and collection by clicking "Create Database" and "Ac
- 2. Import Data:
  - 2.1. In the new collection, click "Add Data" and choose "Import File."
  - 2.2. Select your JSON or CSV file containing the data.
  - 2.3. Map fields if necessary and click "Import."
- 3. Verify Data:
  - 3.1. Explore the imported data in MongoDB Compass to ensure it's displayed co

Now, you have successfully set up a free cluster on MongoDB Atlas, connected Mon the cluster, and imported data into your MongoDB database. This process allows yo with your data using MongoDB's powerful tools.

TIP: We recommend checking the Mongo website for the latest installation instr FAQ in case you run into any issues.

**Example:** [Querying,](https://www.mongodb.com/docs/compass/current/query/filter/) Filtering, and Sorting. Using the Mongo Compass GUI we can filt dataset per our needs. For example, let's see which papers in Medicine were cited th 5 years, and exclude any papers with under 50 citations. In the *project* field we choo would like to display in the output, and we sort in descending order by *citationcount* 

```
{
       's2fieldsofstudy.category': 'Medicine',
       'citationcount': {
             '$gte': 50
       },
        'year': {
             '$gte': 2019,
             '$lte': 2023
       }
  }
Filter<sup>12</sup> O v {"s2fieldsofstudy.category": "Medicine", "citationcount": {"$gte": 50}, "year": {"$gte": 1
              {T-id": 0, "title": 1, "citationcount": 1, "year": 1}
 Project
 Sort
             {Tctationcount": -1}Collation
              { locale: 'simple' }
Output:
  i EXPORT DATA <sub>▼</sub>
    title: "Are We Speaking the Same Language? Recommendations for a Definition an..."
    year: 2019
    citationcount: 1048
    title: "Colorectal Cancer Screening in Average-Risk Adults"
    year: 2019
    citationcount: 704
    title: "Prenatal Genetic Testing and Screening: Constructing Needs and Reinfor..."
    year: 2020
    citationcount: 620
    title: "Urgent Guidance for Navigating and Circumventing the QTc-Prolonging an..."
    year: 2020
    citationcount: 340
    title: "Obesity and mortality of COVID-19. Meta-analysis"
    year: 2020
    citationcount: 338
```

### Working with Multiple Datasets

Oftentimes we may want to combine information from multiple datasets to gather in the following example:

Use case: Let's delve into a publication venue, such as the "Journal of the Geologica learn more about the papers that have been published in it. Perhaps we would like to names of authors who have published a paper in this journal, but only those whose p cited at least 15 times. We can combine information from the publication venues da papers dataset to find the authors that meet this criteria. To do this, we can load our pandas dataframes and retrieve the publication venue ID associated with the "Journ Geological Society" from the *publication venues* dataset. Then we can search the  $p\epsilon$ papers that have a *citationcount* of at least 15 and are tagged to that venue ID. Final the names of authors associated with each of those papers that met our criteria. Fro can explore other possibilities, such as viewing other papers published by those auth their homepage on the Semantic Scholar website, and more.

#### Python Example:

```
import pandas as pd
# Create Pandas DataFrames
papers_df = pd.read_json('papersDataset', lines=True)
venues_df = pd.read_json('publicationVenuesDataset', lines=True)
# Find the venue id for our publication venue of interest - "Journa
publication_venue_id = venues_df.loc[venues_df["name"] == "Journal 
# Filter papers based on the venue id with a citation count of at
filtered_geology_papers = papers_df.loc[
    (papers_df['publicationvenueid''] == publication_venueid')]
# Traverse the list of authors for each paper that met our filter \epsilon
```

```
author_names = []
for authors_list in filtered_geology_papers["authors"]:
```

```
author_names.extend(author["name"] for author in authors_list)
```

# Print the resulting author names, with each name on a new line print("Authors associated with papers from the Journal of the Geolo print(\*author\_names, sep="\n")

#### Output:

| Authors associated with papers from the Journal of the Geological Society: |
|----------------------------------------------------------------------------|
| J. R. Maynard                                                              |
| P. Wignall                                                                 |
| W. J. Varker                                                               |
| F. Donda                                                                   |
| G. Brancolini                                                              |
| P. O'Brien                                                                 |
| L. D. Santis                                                               |
| C. Escutia                                                                 |
| P. Ayarza                                                                  |
| Dennis Brown                                                               |
| J. Alvarez-Marrón                                                          |
| C. Juhlin                                                                  |
| ...                                                                        |