#!/usr/bin/env python3
"""
Test the 4-phase multimodal research system with a small number of agents
"""

from multimodal_research_orchestrator import MultimodalResearchOrchestrator
import time

def test_phase_question_generation():
    """Test phase-specific question generation"""
    print("🧪 Testing Phase-Specific Question Generation")
    print("=" * 50)
    
    orchestrator = MultimodalResearchOrchestrator(silent=True)
    
    # Test each phase with 2 questions
    for phase in range(1, 5):
        print(f"\nPhase {phase}: {orchestrator.phases[phase]}")
        questions = orchestrator.generate_phase_specific_questions(phase, 2)
        
        for i, question in enumerate(questions):
            print(f"  Agent {i+1} Focus: {question.split('Specific Focus Area: ')[1].split('Agent ID:')[0].strip()}")
    
    print("✅ Question generation test complete!")

def test_single_phase():
    """Test running a single phase with 2 agents"""
    print("\n🚀 Testing Single Phase Execution (Phase 1, 2 agents)")
    print("=" * 50)
    
    orchestrator = MultimodalResearchOrchestrator(silent=False)
    
    # Override configuration for testing
    orchestrator.agents_per_phase = 2
    orchestrator.max_concurrent = 1
    orchestrator.batch_delay = 1
    
    start_time = time.time()
    
    try:
        # Test Phase 1
        phase_results = orchestrator.run_phase(1)
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 Phase 1 Test Results:")
        print(f"   ✅ Successful: {phase_results['successful']}")
        print(f"   ❌ Failed: {phase_results['failed']}")
        print(f"   ⏱️ Execution time: {execution_time:.1f}s")
        
        # Test synthesis
        print(f"\n🔄 Testing phase synthesis...")
        synthesis = orchestrator.synthesize_phase_results(phase_results)
        
        print(f"✅ Synthesis complete!")
        print(f"📝 Synthesis preview: {synthesis[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Phase test failed: {e}")
        return False

def main():
    """Run 4-phase system tests"""
    print("🧪 4-Phase Multimodal Research System Test")
    print("=" * 70)
    
    # Test 1: Question Generation
    test_phase_question_generation()
    
    # Test 2: Single Phase Execution
    phase_success = test_single_phase()
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print(f"✅ Question Generation: PASSED")
    print(f"✅ Phase Execution: {'PASSED' if phase_success else 'FAILED'}")
    
    if phase_success:
        print(f"\n🎉 4-Phase system is ready!")
        print(f"\n📋 To run the full research:")
        print(f"   python run_multimodal_research.py")
        print(f"\n⚠️  Full research will take 3-4 hours with 100 agents")
        return True
    else:
        print(f"\n❌ System needs debugging before full deployment")
        return False

if __name__ == "__main__":
    main()
