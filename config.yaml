# API Configuration - Choose your preferred setup
# Option 1: OpenRouter (Recommended - supports multiple models)
openrouter:
  api_key: "YOUR_OPENROUTER_KEY_HERE"  # Get from https://openrouter.ai/
  base_url: "https://openrouter.ai/api/v1"
  model: "google/gemini-2.5-pro"  # High context window model

# Option 2: Direct Gemini API (Alternative setup)
gemini:
  api_key: "AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI"  # Your Gemini API key
  model: "gemini-2.5-pro"

# System prompt for the agent
system_prompt: |
  You are a helpful research assistant. When users ask questions that require 
  current information or web search, use the search tool and all other tools available to find relevant 
  information and provide comprehensive answers based on the results.
  
  IMPORTANT: When you have fully satisfied the user's request and provided a complete answer, 
  you MUST call the mark_task_complete tool with a summary of what was accomplished and 
  a final message for the user. This signals that the task is finished.

# Agent settings
agent:
  max_iterations: 10

# Orchestrator settings
orchestrator:
  parallel_agents: 100  # Total number of agents for large-scale orchestration
  task_timeout: 600     # Timeout per agent in seconds (10 minutes)
  max_concurrent: 2     # RESTORED: 2 concurrent agents (DBLP has no rate limits)
  batch_delay: 5        # RESTORED: 5 seconds between batches (DBLP is reliable)
  share_knowledge_base: true  # Share knowledge base across agents
  aggregation_strategy: "consensus"  # How to combine results
  
  # Question generation prompt for orchestrator
  question_generation_prompt: |
    You are an orchestrator that needs to create {num_agents} different questions to thoroughly analyze this topic from multiple angles.
    
    Original user query: {user_input}
    
    Generate exactly {num_agents} different, specific questions that will help gather comprehensive information about this topic.
    Each question should approach the topic from a different angle (research, analysis, verification, alternatives, etc.).
    
    Return your response as a JSON array of strings, like this:
    ["question 1", "question 2", "question 3", "question 4"]
    
    Only return the JSON array, nothing else.

  # Synthesis prompt for combining all agent responses
  synthesis_prompt: |
    You have {num_responses} different AI agents that analyzed the same query from different perspectives. 
    Your job is to synthesize their responses into ONE comprehensive final answer.
    
    Here are all the agent responses:
    
    {agent_responses}
    
    IMPORTANT: Just synthesize these into ONE final comprehensive answer that combines the best information from all agents. 
    Do NOT call mark_task_complete or any other tools. Do NOT mention that you are synthesizing multiple responses. 
    Simply provide the final synthesized answer directly as your response.

# Search tool settings
search:
  max_results: 5
  user_agent: "Mozilla/5.0 (compatible; OpenRouter Agent)"

# DBLP API settings (Primary - No rate limits!)
dblp:
  base_url: "https://dblp.org/search/publ/api"
  author_url: "https://dblp.org/search/author/api"
  venue_url: "https://dblp.org/search/venue/api"
  max_results: 1000  # DBLP maximum

# Semantic Scholar API settings (BACKUP - Rate limited)
semantic_scholar:
  api_key: "zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn"  # Your provided API key (saved for backup)
  base_url: "https://api.semanticscholar.org/graph/v1"
  rate_limit: 1  # 1 request per second maximum (STRICT LIMIT)
  status: "enabled_with_strict_rate_limiting"
  delay_between_requests: 1.5  # 1.5 seconds between requests for safety

# GitHub API settings
github:
  token: "****************************************"
  base_url: "https://api.github.com"

# Knowledge base settings
knowledge_base:
  path: "D:/Downloads/make-it-heavy-main/research_knowledge_base"
  checkpoint_path: "D:/Downloads/make-it-heavy-main/research_checkpoints"