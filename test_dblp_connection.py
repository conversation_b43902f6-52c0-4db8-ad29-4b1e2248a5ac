#!/usr/bin/env python3
"""Test DBLP API connection"""

import requests

def test_dblp_connection():
    """Test DBLP API connection"""
    try:
        test_url = "https://dblp.org/search/publ/api?q=machine+learning&h=1&format=json"
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            total = data.get('result', {}).get('hits', {}).get('@total', 0)
            print(f"✅ DBLP API connection successful - {total} papers available for 'machine learning'")
            return True
        else:
            print(f"⚠ DBLP API connection issue (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"⚠ DBLP API test failed: {e}")
        return False

if __name__ == "__main__":
    test_dblp_connection()
