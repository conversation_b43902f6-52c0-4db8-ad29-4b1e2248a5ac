#!/usr/bin/env python3
"""
DBLP Search Tool Implementation

DBLP (Database Systems and Logic Programming) is a computer science bibliography
that provides excellent coverage of CS publications with much more lenient rate limiting
than Semantic Scholar.

Features:
- No API key required
- Much higher rate limits
- Excellent coverage of computer science publications
- Simple REST API interface
- JSON and XML output formats
"""

import time
import requests
import xml.etree.ElementTree as ET
from abc import ABC, abstractmethod
from typing import Dict, List, Any
import urllib.parse

class BaseTool(ABC):
    """Base class for all tools"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> dict:
        pass
    
    @abstractmethod
    def execute(self, **kwargs) -> dict:
        pass

class DBLPSearchTool(BaseTool):
    """Tool for searching academic papers using DBLP API"""
    
    def __init__(self):
        self.base_url = "https://dblp.org/search/publ/api"
        self.author_url = "https://dblp.org/search/author/api"
        self.venue_url = "https://dblp.org/search/venue/api"
        
    @property
    def name(self) -> str:
        return "search_papers"
    
    @property
    def description(self) -> str:
        return "Search for academic papers using DBLP (Database Systems and Logic Programming) API"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for papers (supports boolean operators: AND with space, OR with |)"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results (max 1000)",
                    "default": 20,
                    "maximum": 1000
                },
                "search_type": {
                    "type": "string",
                    "enum": ["publications", "authors", "venues"],
                    "description": "Type of search to perform",
                    "default": "publications"
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020' for specific year, '2020-2023' for range)"
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, limit: int = 20, search_type: str = "publications", 
                year_filter: str = None, **kwargs) -> dict:
        try:
            # Choose the appropriate endpoint
            if search_type == "authors":
                url = self.author_url
            elif search_type == "venues":
                url = self.venue_url
            else:
                url = self.base_url
            
            # Prepare parameters
            params = {
                "q": query,
                "h": min(limit, 1000),  # DBLP max is 1000
                "format": "json"
            }
            
            # Add year filter if specified
            if year_filter and search_type == "publications":
                # DBLP doesn't have direct year filtering in API, but we can add it to query
                if "-" in year_filter:
                    # Range filter not directly supported, will filter in post-processing
                    pass
                else:
                    # Single year can be added to query
                    params["q"] = f"{query} {year_filter}"
            
            print(f"🔍 Searching DBLP for: '{query}' (type: {search_type}, limit: {limit})")
            
            # Make the request
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            
            if search_type == "publications":
                return self._process_publications(data, query, year_filter)
            elif search_type == "authors":
                return self._process_authors(data, query)
            elif search_type == "venues":
                return self._process_venues(data, query)
                
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "error": f"DBLP API request failed: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": f"DBLP processing error: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_publications(self, data: dict, query: str, year_filter: str = None) -> dict:
        """Process publication search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            papers = hits.get("hit", [])
            
            # Ensure papers is a list
            if isinstance(papers, dict):
                papers = [papers]
            
            processed_papers = []
            
            for paper_hit in papers:
                paper_info = paper_hit.get("info", {})
                
                # Extract basic information
                title = paper_info.get("title", "No title")
                authors = paper_info.get("authors", {}).get("author", [])
                year = paper_info.get("year", "Unknown")
                venue = paper_info.get("venue", "Unknown venue")
                paper_type = paper_info.get("type", "Unknown")
                key = paper_info.get("key", "")
                
                # Process authors
                if isinstance(authors, dict):
                    authors = [authors]
                
                author_names = []
                for author in authors:
                    if isinstance(author, dict):
                        author_names.append(author.get("text", "Unknown"))
                    else:
                        author_names.append(str(author))
                
                # Apply year filter if specified
                if year_filter and year != "Unknown":
                    try:
                        paper_year = int(year)
                        if "-" in year_filter:
                            start_year, end_year = map(int, year_filter.split("-"))
                            if not (start_year <= paper_year <= end_year):
                                continue
                        else:
                            filter_year = int(year_filter)
                            if paper_year != filter_year:
                                continue
                    except (ValueError, TypeError):
                        pass  # Skip year filtering if conversion fails
                
                # Create paper entry
                paper_entry = {
                    "title": title,
                    "authors": author_names,
                    "year": year,
                    "venue": venue,
                    "type": paper_type,
                    "dblp_key": key,
                    "url": f"https://dblp.org/rec/{key}" if key else None
                }
                
                processed_papers.append(paper_entry)
            
            return {
                "status": "success",
                "papers": processed_papers,
                "total_found": len(processed_papers),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "publications"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing publications: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_authors(self, data: dict, query: str) -> dict:
        """Process author search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            authors = hits.get("hit", [])
            
            if isinstance(authors, dict):
                authors = [authors]
            
            processed_authors = []
            
            for author_hit in authors:
                author_info = author_hit.get("info", {})
                
                author_entry = {
                    "name": author_info.get("author", "Unknown"),
                    "dblp_key": author_info.get("url", ""),
                    "url": author_info.get("url", "")
                }
                
                processed_authors.append(author_entry)
            
            return {
                "status": "success",
                "authors": processed_authors,
                "total_found": len(processed_authors),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "authors"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing authors: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_venues(self, data: dict, query: str) -> dict:
        """Process venue search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            venues = hits.get("hit", [])
            
            if isinstance(venues, dict):
                venues = [venues]
            
            processed_venues = []
            
            for venue_hit in venues:
                venue_info = venue_hit.get("info", {})
                
                venue_entry = {
                    "name": venue_info.get("venue", "Unknown"),
                    "type": venue_info.get("type", "Unknown"),
                    "dblp_key": venue_info.get("url", ""),
                    "url": venue_info.get("url", "")
                }
                
                processed_venues.append(venue_entry)
            
            return {
                "status": "success",
                "venues": processed_venues,
                "total_found": len(processed_venues),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "venues"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing venues: {str(e)}",
                "query": query,
                "source": "DBLP"
            }

# Example usage and testing
if __name__ == "__main__":
    tool = DBLPSearchTool()
    
    # Test publication search
    print("Testing DBLP publication search...")
    result = tool.execute("dataset distillation", limit=5)
    print(f"Publications result: {result}")
    
    # Test author search
    print("\nTesting DBLP author search...")
    result = tool.execute("Geoffrey Hinton", search_type="authors", limit=3)
    print(f"Authors result: {result}")
    
    # Test venue search
    print("\nTesting DBLP venue search...")
    result = tool.execute("ICML", search_type="venues", limit=3)
    print(f"Venues result: {result}")
