import json
import yaml
import re
import google.generativeai as genai
from tools import discover_tools

class GeminiAgent:
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)

        # Silent mode for orchestrator (suppresses debug output)
        self.silent = silent

        # Initialize Gemini API
        if 'gemini' not in self.config or not self.config['gemini']['api_key']:
            raise ValueError("Gemini API key not found in config.yaml")

        genai.configure(api_key=self.config['gemini']['api_key'])
        self.model = genai.GenerativeModel(self.config['gemini']['model'])

        # Discover tools dynamically
        self.discovered_tools = discover_tools(self.config, silent=self.silent)

        # Build tool mapping
        self.tool_mapping = {name: tool.execute for name, tool in self.discovered_tools.items()}

        # Create tool descriptions for prompt-based tool calling
        self.tools_description = self._create_tools_description()

    def _create_tools_description(self):
        """Create a description of available tools for prompt-based calling"""
        tools_desc = "Available tools:\n"
        for name, tool in self.discovered_tools.items():
            tools_desc += f"- {name}: {tool.description}\n"

        tools_desc += "\nTo use a tool, respond with: TOOL_CALL: tool_name(param1=value1, param2=value2)\n"
        tools_desc += "When task is complete, use: TOOL_CALL: mark_task_complete(summary='your summary', final_message='your message')\n"
        return tools_desc

    def call_llm(self, messages):
        """Make Gemini API call"""
        try:
            # Convert messages to Gemini format
            prompt = self._convert_messages_to_prompt(messages)

            # Add tools description to the prompt
            enhanced_prompt = f"{prompt}\n\n{self.tools_description}"

            # Configure generation
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=2000,
                temperature=0.7,
                top_p=0.8,
                top_k=40
            )

            response = self.model.generate_content(
                enhanced_prompt,
                generation_config=generation_config
            )

            return response

        except Exception as e:
            raise Exception(f"Gemini API call failed: {str(e)}")
    
    def _convert_messages_to_prompt(self, messages):
        """Convert OpenAI-style messages to Gemini prompt"""
        prompt_parts = []

        for message in messages:
            role = message.get("role", "")
            content = message.get("content", "")

            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
            elif role == "tool":
                prompt_parts.append(f"Tool Result: {content}")

        return "\n\n".join(prompt_parts)

    def _parse_tool_calls(self, text):
        """Parse tool calls from Gemini response text"""
        tool_calls = []

        # Look for TOOL_CALL: pattern
        tool_call_pattern = r'TOOL_CALL:\s*(\w+)\((.*?)\)'
        matches = re.findall(tool_call_pattern, text, re.DOTALL)

        for tool_name, args_str in matches:
            try:
                # Parse arguments
                args = {}
                if args_str.strip():
                    # Simple parsing for key=value pairs
                    arg_pairs = re.findall(r'(\w+)=([^,)]+)', args_str)
                    for key, value in arg_pairs:
                        # Remove quotes and clean up
                        value = value.strip().strip('"\'')
                        args[key] = value

                tool_calls.append({
                    'name': tool_name,
                    'args': args
                })
            except Exception as e:
                if not self.silent:
                    print(f"⚠️ Failed to parse tool call: {e}")

        return tool_calls

    def handle_tool_call(self, tool_call):
        """Handle a parsed tool call and return the result"""
        try:
            tool_name = tool_call['name']
            tool_args = tool_call['args']

            # Call appropriate tool from tool_mapping
            if tool_name in self.tool_mapping:
                tool_result = self.tool_mapping[tool_name](**tool_args)
            else:
                tool_result = {"error": f"Unknown tool: {tool_name}"}

            return {
                "role": "tool",
                "name": tool_name,
                "content": json.dumps(tool_result)
            }

        except Exception as e:
            return {
                "role": "tool",
                "name": tool_call.get('name', 'unknown'),
                "content": json.dumps({"error": f"Tool execution failed: {str(e)}"})
            }
    
    def run(self, user_input: str):
        """Run the agent with user input and return FULL conversation content"""
        # Initialize messages with system prompt and user input
        messages = [
            {
                "role": "system",
                "content": self.config.get('system_prompt', 'You are a helpful research assistant.')
            },
            {
                "role": "user",
                "content": user_input
            }
        ]

        # Track all assistant responses for full content capture
        full_response_content = []

        # Implement agentic loop
        max_iterations = self.config.get('agent', {}).get('max_iterations', 10)
        iteration = 0

        while iteration < max_iterations:
            iteration += 1
            if not self.silent:
                print(f"🔄 Gemini Agent iteration {iteration}/{max_iterations}")

            # Call Gemini
            response = self.call_llm(messages)

            # Extract text content with better error handling
            response_text = ""
            try:
                if hasattr(response, 'text') and response.text:
                    response_text = response.text
                elif hasattr(response, 'candidates') and response.candidates:
                    # Check if response was blocked
                    candidate = response.candidates[0]
                    if hasattr(candidate, 'finish_reason') and candidate.finish_reason == 2:
                        # Response was blocked, provide a fallback
                        response_text = "I apologize, but I need to rephrase my response. Let me try a different approach to help you."
                    elif hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                        # Try to extract text from parts
                        for part in candidate.content.parts:
                            if hasattr(part, 'text'):
                                response_text += part.text
            except Exception as e:
                if not self.silent:
                    print(f"⚠️ Error extracting response text: {e}")
                response_text = "I encountered an issue processing the response. Let me try again."

            if response_text:
                # Add to full response content
                full_response_content.append(response_text)

                # Add assistant message
                messages.append({
                    "role": "assistant",
                    "content": response_text
                })

                # Parse tool calls from response text
                tool_calls = self._parse_tool_calls(response_text)

                if tool_calls:
                    if not self.silent:
                        print(f"🔧 Gemini Agent making {len(tool_calls)} tool call(s)")

                    task_completed = False
                    for tool_call in tool_calls:
                        if not self.silent:
                            print(f"   📞 Calling tool: {tool_call['name']}")

                        # Handle tool call
                        tool_result = self.handle_tool_call(tool_call)
                        messages.append(tool_result)

                        # Check if this was the task completion tool
                        if tool_call['name'] == "mark_task_complete":
                            task_completed = True
                            if not self.silent:
                                print("✅ Task completion tool called - exiting loop")
                            return "\n\n".join(full_response_content)

                    # If task was completed, we already returned above
                    if task_completed:
                        return "\n\n".join(full_response_content)
                else:
                    # No tool calls found, might be a regular response
                    if not self.silent:
                        print("💭 Agent responded without tool calls")
            else:
                if not self.silent:
                    print("⚠️ Empty response from Gemini")
                break

        # Return whatever content we gathered
        return "\n\n".join(full_response_content) if full_response_content else "Maximum iterations reached."
