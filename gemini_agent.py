import json
import yaml
import google.generativeai as genai
from tools import discover_tools

class GeminiAgent:
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Silent mode for orchestrator (suppresses debug output)
        self.silent = silent
        
        # Initialize Gemini API
        if 'gemini' not in self.config or not self.config['gemini']['api_key']:
            raise ValueError("Gemini API key not found in config.yaml")
        
        genai.configure(api_key=self.config['gemini']['api_key'])
        self.model = genai.GenerativeModel(self.config['gemini']['model'])
        
        # Discover tools dynamically
        self.discovered_tools = discover_tools(self.config, silent=self.silent)
        
        # Build tool mapping for function calling
        self.tool_mapping = {name: tool.execute for name, tool in self.discovered_tools.items()}
        
        # Convert tools to Gemini function declarations
        self.function_declarations = []
        for tool in self.discovered_tools.values():
            func_declaration = {
                "name": tool.name,
                "description": tool.description,
                "parameters": {
                    "type": "object",
                    "properties": tool.parameters.get("properties", {}),
                    "required": tool.parameters.get("required", [])
                }
            }
            self.function_declarations.append(func_declaration)
    
    def call_llm(self, messages):
        """Make Gemini API call with function calling"""
        try:
            # Convert messages to Gemini format
            prompt = self._convert_messages_to_prompt(messages)

            # Configure generation
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=2000,
                temperature=0.7,
                top_p=0.8,
                top_k=40
            )

            # For now, use simple generation without function calling
            # TODO: Implement proper function calling when needed
            response = self.model.generate_content(
                prompt,
                generation_config=generation_config
            )

            return response

        except Exception as e:
            raise Exception(f"Gemini API call failed: {str(e)}")
    
    def _convert_messages_to_prompt(self, messages):
        """Convert OpenAI-style messages to Gemini prompt"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
            elif role == "tool":
                prompt_parts.append(f"Tool Result: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def handle_function_call(self, function_call):
        """Handle a Gemini function call and return the result"""
        try:
            # Extract function name and arguments
            function_name = function_call.name
            function_args = {}
            
            # Parse arguments from Gemini function call
            if hasattr(function_call, 'args'):
                function_args = dict(function_call.args)
            
            # Call appropriate tool from tool_mapping
            if function_name in self.tool_mapping:
                tool_result = self.tool_mapping[function_name](**function_args)
            else:
                tool_result = {"error": f"Unknown function: {function_name}"}
            
            return {
                "role": "tool",
                "name": function_name,
                "content": json.dumps(tool_result)
            }
        
        except Exception as e:
            return {
                "role": "tool",
                "name": function_name,
                "content": json.dumps({"error": f"Function execution failed: {str(e)}"})
            }
    
    def run(self, user_input: str):
        """Run the agent with user input and return FULL conversation content"""
        # Initialize messages with system prompt and user input
        messages = [
            {
                "role": "system",
                "content": self.config.get('system_prompt', 'You are a helpful research assistant.')
            },
            {
                "role": "user",
                "content": user_input
            }
        ]
        
        # Track all assistant responses for full content capture
        full_response_content = []
        
        # Implement agentic loop
        max_iterations = self.config.get('agent', {}).get('max_iterations', 10)
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            if not self.silent:
                print(f"🔄 Gemini Agent iteration {iteration}/{max_iterations}")
            
            # Call Gemini
            response = self.call_llm(messages)
            
            # Extract text content
            response_text = response.text if hasattr(response, 'text') and response.text else ""
            
            if response_text:
                full_response_content.append(response_text)
                messages.append({
                    "role": "assistant",
                    "content": response_text
                })
            
            # Check for function calls
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call'):
                            if not self.silent:
                                print(f"🔧 Gemini Agent calling function: {part.function_call.name}")
                            
                            # Handle function call
                            function_result = self.handle_function_call(part.function_call)
                            messages.append(function_result)
                            
                            # Check if this was the task completion function
                            if part.function_call.name == "mark_task_complete":
                                if not self.silent:
                                    print("✅ Task completion function called - exiting loop")
                                return "\n\n".join(full_response_content)
            
            # If no function calls and we have content, we might be done
            if response_text and not hasattr(response, 'candidates'):
                break
        
        # Return whatever content we gathered
        return "\n\n".join(full_response_content) if full_response_content else "Maximum iterations reached."
