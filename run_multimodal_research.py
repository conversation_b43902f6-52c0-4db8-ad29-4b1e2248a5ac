#!/usr/bin/env python3
"""
Main execution script for 4-Phase Multimodal Dataset Distillation Research
Implements your comprehensive research directive with 100 agents (25 per phase)
"""

import time
import json
import os
from datetime import datetime
from multimodal_research_orchestrator import MultimodalResearchOrchestrator

def save_results(results: dict, session_id: str):
    """Save research results to files"""
    
    # Create results directory
    results_dir = f"./multimodal_research_results_{session_id}"
    os.makedirs(results_dir, exist_ok=True)
    
    # Save complete results as JSON
    with open(f"{results_dir}/complete_results.json", 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Save final synthesis as markdown
    with open(f"{results_dir}/final_synthesis.md", 'w') as f:
        f.write("# Advancing Multimodal Dataset Distillation Research\n\n")
        f.write(f"**Research Session:** {session_id}\n")
        f.write(f"**Completion Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**Total Agents:** {results['total_agents']}\n")
        f.write(f"**Success Rate:** {results['statistics']['success_rate']:.1f}%\n\n")
        f.write("## Final Comprehensive Synthesis\n\n")
        f.write(results['final_synthesis'])
    
    # Save individual phase syntheses
    for phase_syn in results['phase_syntheses']:
        phase_file = f"{results_dir}/phase_{phase_syn['phase']}_synthesis.md"
        with open(phase_file, 'w') as f:
            f.write(f"# Phase {phase_syn['phase']}: {phase_syn['phase_name']}\n\n")
            f.write(phase_syn['synthesis'])
    
    print(f"📁 Results saved to: {results_dir}")
    return results_dir

def display_progress_summary(results: dict):
    """Display a summary of the research progress"""
    print("\n" + "="*80)
    print("🎯 MULTIMODAL DATASET DISTILLATION RESEARCH SUMMARY")
    print("="*80)
    
    print(f"📊 Overall Statistics:")
    print(f"   • Total Agents: {results['total_agents']}")
    print(f"   • Successful: {results['statistics']['total_successful']}")
    print(f"   • Failed: {results['statistics']['total_failed']}")
    print(f"   • Success Rate: {results['statistics']['success_rate']:.1f}%")
    print(f"   • Total Time: {results['total_execution_time']/60:.1f} minutes")
    print(f"   • Avg Time per Agent: {results['statistics']['avg_time_per_agent']:.1f}s")
    
    print(f"\n📋 Phase-by-Phase Results:")
    for phase_result in results['phase_results']:
        print(f"   Phase {phase_result['phase']}: {phase_result['successful']}/{phase_result['total_agents']} successful")
    
    print(f"\n🔬 Research Phases Completed:")
    for phase_syn in results['phase_syntheses']:
        print(f"   ✅ Phase {phase_syn['phase']}: {phase_syn['phase_name']}")
    
    print(f"\n📝 Final Synthesis Preview:")
    preview = results['final_synthesis'][:300] + "..." if len(results['final_synthesis']) > 300 else results['final_synthesis']
    print(f"   {preview}")

def main():
    """Main execution function"""
    print("🚀 MULTIMODAL DATASET DISTILLATION RESEARCH ORCHESTRATOR")
    print("="*80)
    print("Research Directive: Advancing Multimodal Dataset Distillation")
    print("Target Dataset: MMIS (Multimodal Dataset for Interior Scene Visual Generation)")
    print("Modalities: Image, Text, Audio")
    print("="*80)
    
    # Create session ID
    session_id = f"multimodal_research_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"📋 Session ID: {session_id}")
    
    # Initialize orchestrator
    print(f"\n🔧 Initializing Research Orchestrator...")
    orchestrator = MultimodalResearchOrchestrator(silent=False)
    orchestrator.set_session_id(session_id)
    
    print(f"\n📋 Research Configuration:")
    print(f"   • Total Agents: 100")
    print(f"   • Agents per Phase: 25")
    print(f"   • Max Concurrent: 2")
    print(f"   • Batch Delay: 5 seconds")
    print(f"   • Estimated Duration: ~3-4 hours")
    
    # Confirm execution
    print(f"\n⚠️  This will run 100 AI agents across 4 research phases.")
    print(f"⚠️  Estimated cost: Moderate (depending on Gemini API usage)")
    print(f"⚠️  Estimated time: 3-4 hours")
    
    confirm = input(f"\n🤔 Do you want to proceed? (yes/no): ").strip().lower()
    
    if confirm not in ['yes', 'y']:
        print("❌ Research cancelled by user.")
        return
    
    print(f"\n🚀 Starting 4-Phase Research Orchestration...")
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run the complete research
        results = orchestrator.run_complete_research()
        
        # Display summary
        display_progress_summary(results)
        
        # Save results
        results_dir = save_results(results, session_id)
        
        print(f"\n🎉 RESEARCH COMPLETED SUCCESSFULLY!")
        print(f"📁 Results saved to: {results_dir}")
        print(f"📝 Key files:")
        print(f"   • complete_results.json - Full research data")
        print(f"   • final_synthesis.md - Comprehensive research synthesis")
        print(f"   • phase_X_synthesis.md - Individual phase results")
        
        print(f"\n🔬 Research Achievements:")
        print(f"   ✅ Comprehensive limitation analysis completed")
        print(f"   ✅ Data informativeness assessment framework developed")
        print(f"   ✅ Novel MFDD algorithmic framework designed")
        print(f"   ✅ Verification and evaluation protocols established")
        
        return results
        
    except KeyboardInterrupt:
        print(f"\n⏹️ Research interrupted by user.")
        return None
    except Exception as e:
        print(f"\n❌ Research failed: {e}")
        return None

if __name__ == "__main__":
    results = main()
    
    if results:
        print(f"\n🎯 Next Steps:")
        print(f"   1. Review the final synthesis document")
        print(f"   2. Implement the proposed MFDD framework")
        print(f"   3. Conduct experimental validation")
        print(f"   4. Prepare research publication")
        print(f"\n🚀 Your multimodal dataset distillation research is ready for implementation!")
    else:
        print(f"\n❌ Research incomplete. Please check the logs and try again.")
