# Complete Make It Heavy - Gemini Edition with Semantic Scholar + Local Knowledge Base

import google.generativeai as genai
import json
import yaml
import time
import os
import requests
import re
from typing import Dict, List, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from IPython.display import display, HTML, clear_output
import ipywidgets as widgets
from datetime import datetime
from abc import ABC, abstractmethod
from google.colab import drive
import glob
import hashlib
from collections import defaultdict
import random
from google.api_core import exceptions
from google.colab import userdata


# Global rate limiter for Semantic Scholar
class RateLimiter:
    def __init__(self, min_interval=1.0):
        self.min_interval = min_interval
        self.last_call = 0
        self.lock = threading.Lock()

    def wait(self):
        with self.lock:
            current = time.time()
            time_since_last = current - self.last_call
            if time_since_last < self.min_interval:
                sleep_time = self.min_interval - time_since_last
                time.sleep(sleep_time)
            self.last_call = time.time()

# Create global rate limiter
import threading
s2_rate_limiter = RateLimiter(min_interval=1.0)



# Mount Google Drive
try:
    drive.mount('/content/drive')
    print("✅ Google Drive mounted successfully")
except:
    print("⚠️ Google Drive not mounted. Local knowledge base will not be available.")

# Configuration
CONFIG = {
    "gemini": {
        "api_key": "AIzaSyATu3l4pGAffi2EcXDdAAe8o9z1q2yyrY8", #"AIzaSyD0dPqnPNx4-yFyEMWw8RHaUvo1hUPzh7U",
        "model": "gemini-2.5-pro"
    },
    "agent": {
        "max_iterations": 10
    },
    "orchestrator": {
        "parallel_agents": 100,
        "task_timeout": 600,
        "max_concurrent": 1,  # Run 4 at a time
        "batch_delay": 5,  # Seconds between batches
        "share_knowledge_base": True  # Share KB across agents
    },
    "semantic_scholar": {
        "api_key": "zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn",  # Replace with your key
        "base_url": "https://api.semanticscholar.org/graph/v1"
    },
    "knowledge_base": {
        "base_path": "/content/drive/MyDrive/BASE KNOWLEDGE",
        "file_extension": "*.md",
        "chunk_size": 2000,  # Characters per chunk for processing
        "max_results": 5  # Maximum number of relevant documents to return
    }
}

# Configure Gemini
genai.configure(api_key=CONFIG['gemini']['api_key'])

# ============== BASE TOOL CLASS ==============
class BaseTool(ABC):
    @property
    @abstractmethod
    def name(self) -> str:
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        pass

    @property
    @abstractmethod
    def parameters(self) -> dict:
        pass

    @abstractmethod
    def execute(self, **kwargs) -> dict:
        pass

# ============== LOCAL KNOWLEDGE BASE TOOL ==============
class LocalKnowledgeBaseTool(BaseTool):
    def __init__(self, base_path: str = None):
        self.base_path = base_path or CONFIG['knowledge_base']['base_path']
        self.chunk_size = CONFIG['knowledge_base']['chunk_size']
        self.max_results = CONFIG['knowledge_base']['max_results']
        self.documents = {}
        self.document_chunks = {}
        self.chunk_to_doc = {}
        self._load_documents()

    def _load_documents(self):
        """Load all markdown documents from the knowledge base"""
        try:
            if not os.path.exists(self.base_path):
                os.makedirs(self.base_path, exist_ok=True)
                print(f"📁 Created knowledge base directory: {self.base_path}")
                return

            md_files = glob.glob(os.path.join(self.base_path, "**/*.md"), recursive=True)

            for file_path in md_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Extract filename without path
                    filename = os.path.basename(file_path)

                    # Store document
                    self.documents[filename] = {
                        'path': file_path,
                        'content': content,
                        'title': self._extract_title(content, filename),
                        'length': len(content)
                    }

                    # Create chunks for similarity search
                    chunks = self._create_chunks(content)
                    self.document_chunks[filename] = chunks

                    # Map chunks to documents
                    for i, chunk in enumerate(chunks):
                        chunk_id = f"{filename}_chunk_{i}"
                        self.chunk_to_doc[chunk_id] = filename

                except Exception as e:
                    print(f"Error loading {file_path}: {e}")

            print(f"📚 Loaded {len(self.documents)} documents from knowledge base")

        except Exception as e:
            print(f"Error loading knowledge base: {e}")

    def _extract_title(self, content: str, filename: str) -> str:
        """Extract title from markdown content"""
        # Look for # Title at the beginning
        lines = content.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            if line.strip().startswith('# '):
                return line.strip()[2:]

        # Fallback to filename without extension
        return filename.replace('.md', '').replace('_', ' ').title()

    def _create_chunks(self, content: str) -> List[str]:
        """Split content into chunks for processing"""
        # Clean content
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Normalize newlines

        # Split by paragraphs first
        paragraphs = content.split('\n\n')

        chunks = []
        current_chunk = ""

        for para in paragraphs:
            if len(current_chunk) + len(para) < self.chunk_size:
                current_chunk += para + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = para + "\n\n"

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _calculate_relevance(self, query: str, text: str) -> float:
        """Simple relevance scoring based on keyword matching"""
        query_lower = query.lower()
        text_lower = text.lower()

        # Split query into words
        query_words = set(query_lower.split())

        # Count matching words
        matches = sum(1 for word in query_words if word in text_lower)

        # Calculate score
        score = matches / len(query_words) if query_words else 0

        # Boost score if exact phrase is found
        if query_lower in text_lower:
            score *= 2

        return score

    @property
    def name(self) -> str:
        return "search_local_knowledge"

    @property
    def description(self) -> str:
        return "Search through local research papers and documents in the knowledge base. Returns relevant excerpts from your personal collection of research papers."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query to find relevant documents in the local knowledge base"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of relevant documents to return (default: 5)",
                    "default": 5
                },
                "include_content": {
                    "type": "boolean",
                    "description": "Whether to include full content excerpts (default: True)",
                    "default": True
                }
            },
            "required": ["query"]
        }

    def execute(self, query: str, max_results: int = None, include_content: bool = True) -> dict:
        try:
            max_results = max_results or self.max_results

            # Search through all chunks
            chunk_scores = []

            for chunk_id, chunk_content in [
                (cid, self.document_chunks[doc][i])
                for doc in self.document_chunks
                for i, cid in enumerate([f"{doc}_chunk_{j}" for j in range(len(self.document_chunks[doc]))])
            ]:
                score = self._calculate_relevance(query, chunk_content)
                if score > 0:
                    doc_name = self.chunk_to_doc[chunk_id]
                    chunk_scores.append({
                        'chunk_id': chunk_id,
                        'document': doc_name,
                        'score': score,
                        'content': chunk_content[:500] + "..." if len(chunk_content) > 500 else chunk_content
                    })

            # Sort by relevance score
            chunk_scores.sort(key=lambda x: x['score'], reverse=True)

            # Group by document and take best chunks
            doc_results = defaultdict(list)
            for chunk in chunk_scores[:max_results * 2]:  # Get more chunks to ensure document diversity
                doc_results[chunk['document']].append(chunk)

            # Format results
            results = []
            for doc_name, chunks in list(doc_results.items())[:max_results]:
                doc_info = self.documents[doc_name]

                result = {
                    'filename': doc_name,
                    'title': doc_info['title'],
                    'relevance_score': chunks[0]['score'],
                    'num_relevant_chunks': len(chunks)
                }

                if include_content:
                    # Include top 2 chunks from this document
                    result['relevant_excerpts'] = [
                        chunk['content'] for chunk in chunks[:2]
                    ]

                results.append(result)

            # Sort final results by relevance
            results.sort(key=lambda x: x['relevance_score'], reverse=True)

            return {
                'status': 'success',
                'query': query,
                'num_documents_searched': len(self.documents),
                'num_results': len(results),
                'results': results
            }

        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'query': query
            }

# ============== READ DOCUMENT TOOL ==============
class ReadDocumentTool(BaseTool):
    def __init__(self, base_path: str = None):
        self.base_path = base_path or CONFIG['knowledge_base']['base_path']

    @property
    def name(self) -> str:
        return "read_full_document"

    @property
    def description(self) -> str:
        return "Read the full content of a specific document from the local knowledge base"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "filename": {
                    "type": "string",
                    "description": "The filename of the document to read (e.g., 'paper_title.md')"
                },
                "section": {
                    "type": "string",
                    "description": "Optional: specific section to extract (e.g., 'Introduction', 'Methods')",
                    "default": None
                }
            },
            "required": ["filename"]
        }

    def execute(self, filename: str, section: str = None) -> dict:
        try:
            # Find the file
            file_path = None
            if os.path.exists(os.path.join(self.base_path, filename)):
                file_path = os.path.join(self.base_path, filename)
            else:
                # Search recursively
                files = glob.glob(os.path.join(self.base_path, "**", filename), recursive=True)
                if files:
                    file_path = files[0]

            if not file_path or not os.path.exists(file_path):
                return {
                    'status': 'error',
                    'error': f"Document '{filename}' not found in knowledge base"
                }

            # Read content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract section if specified
            if section:
                section_content = self._extract_section(content, section)
                if section_content:
                    return {
                        'status': 'success',
                        'filename': filename,
                        'section': section,
                        'content': section_content,
                        'length': len(section_content)
                    }
                else:
                    return {
                        'status': 'error',
                        'error': f"Section '{section}' not found in document",
                        'available_sections': self._get_sections(content)
                    }

            return {
                'status': 'success',
                'filename': filename,
                'content': content,
                'length': len(content),
                'sections': self._get_sections(content)
            }

        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }

    def _extract_section(self, content: str, section: str) -> Optional[str]:
        """Extract a specific section from markdown content"""
        lines = content.split('\n')
        section_lower = section.lower()

        in_section = False
        section_content = []
        current_level = 0

        for line in lines:
            # Check if this is a header
            if line.strip().startswith('#'):
                header_match = re.match(r'^(#+)\s+(.+)$', line.strip())
                if header_match:
                    level = len(header_match.group(1))
                    header_text = header_match.group(2).lower()

                    if section_lower in header_text:
                        in_section = True
                        current_level = level
                        section_content.append(line)
                    elif in_section and level <= current_level:
                        # End of section
                        break
                    elif in_section:
                        section_content.append(line)
            elif in_section:
                section_content.append(line)

        return '\n'.join(section_content) if section_content else None

    def _get_sections(self, content: str) -> List[str]:
        """Get all section headers from the document"""
        sections = []
        lines = content.split('\n')

        for line in lines:
            if line.strip().startswith('#'):
                header_match = re.match(r'^#+\s+(.+)$', line.strip())
                if header_match:
                    sections.append(header_match.group(1))

        return sections

# ============== SEMANTIC SCHOLAR TOOL ==============
class SemanticScholarTool(BaseTool):
    def __init__(self, api_key: str = None):
        self.api_key = api_key or CONFIG['semantic_scholar']['api_key']
        self.base_url = CONFIG['semantic_scholar']['base_url']
        self.headers = {}
        if self.api_key and self.api_key != "YOUR_SEMANTIC_SCHOLAR_API_KEY_HERE":
            self.headers['x-api-key'] = self.api_key
        self.last_request_time = 0  # Add rate limiting tracking

    def _rate_limit_wait(self):
        """Ensure we don't exceed 1 request per second"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < 1.0:
            time.sleep(1.0 - time_since_last)
        self.last_request_time = time.time()

    @property
    def name(self) -> str:
        return "search_research_papers"

    @property
    def description(self) -> str:
        return "Search for academic research papers using Semantic Scholar. Returns paper titles, authors, abstracts, citations, and links."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for finding research papers"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of papers to return (default: 5, max: 10)",
                    "default": 5
                },
                "fields": {
                    "type": "string",
                    "description": "Comma-separated list of fields to return",
                    "default": "title,authors,abstract,citationCount,year,url,venue,tldr"
                }
            },
            "required": ["query"]
        }

    def execute(self, query: str, limit: int = 5, fields: str = None) -> dict:
        try:
            # Apply rate limiting
            self._rate_limit_wait()

            limit = min(limit, 10)
            if not fields:
                fields = "title,authors,abstract,citationCount,year,url,venue,tldr"

            search_url = f"{self.base_url}/paper/search"
            params = {
                "query": query,
                "limit": limit,
                "fields": fields
            }

            response = requests.get(search_url, params=params, headers=self.headers)

            if response.status_code == 200:
                data = response.json()
                papers = data.get('data', [])

                formatted_papers = []
                for paper in papers:
                    formatted_paper = {
                        "title": paper.get('title', 'No title'),
                        "authors": [author.get('name', 'Unknown') for author in paper.get('authors', [])],
                        "year": paper.get('year', 'Unknown'),
                        "abstract": paper.get('abstract', 'No abstract available'),
                        "citation_count": paper.get('citationCount', 0),
                        "url": paper.get('url', ''),
                        "venue": paper.get('venue', 'Unknown venue'),
                        "tldr": paper.get('tldr', {}).get('text', '') if paper.get('tldr') else ''
                    }
                    formatted_papers.append(formatted_paper)

                return {
                    "status": "success",
                    "query": query,
                    "count": len(formatted_papers),
                    "papers": formatted_papers
                }
            elif response.status_code == 429:
                # Rate limit exceeded
                return {
                    "status": "error",
                    "error": "Rate limit exceeded. Please wait before making another request.",
                    "retry_after": response.headers.get('Retry-After', '1 second')
                }
            else:
                return {
                    "status": "error",
                    "error": f"API request failed with status {response.status_code}",
                    "message": response.text
                }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }


# ============== PAPER DETAILS TOOL ==============
# ============== PAPER DETAILS TOOL ==============
class PaperDetailsTool(BaseTool):
    def __init__(self, api_key: str = None):
        self.api_key = api_key or CONFIG['semantic_scholar']['api_key']
        self.base_url = CONFIG['semantic_scholar']['base_url']
        self.headers = {}
        if self.api_key and self.api_key != "YOUR_SEMANTIC_SCHOLAR_API_KEY_HERE":
            self.headers['x-api-key'] = self.api_key
        self.last_request_time = 0  # Add rate limiting

    def _rate_limit_wait(self):
        """Ensure we don't exceed 1 request per second"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < 1.0:
            time.sleep(1.0 - time_since_last)
        self.last_request_time = time.time()

    @property
    def name(self) -> str:
        return "get_paper_details"

    @property
    def description(self) -> str:
        return "Get detailed information about a specific research paper including references and citations"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "paper_id": {
                    "type": "string",
                    "description": "Paper ID (can be Semantic Scholar ID, DOI, ArXiv ID, etc.)"
                },
                "include_references": {
                    "type": "boolean",
                    "description": "Whether to include paper references",
                    "default": False
                },
                "include_citations": {
                    "type": "boolean",
                    "description": "Whether to include papers that cite this paper",
                    "default": False
                }
            },
            "required": ["paper_id"]
        }

    def execute(self, paper_id: str, include_references: bool = False, include_citations: bool = False) -> dict:
        try:
            # Apply rate limiting
            self._rate_limit_wait()

            fields = "paperId,title,authors,abstract,year,citationCount,referenceCount,fieldsOfStudy,url,venue,publicationTypes,tldr"

            if include_references:
                fields += ",references"
            if include_citations:
                fields += ",citations"

            url = f"{self.base_url}/paper/{paper_id}"
            params = {"fields": fields}

            response = requests.get(url, params=params, headers=self.headers)

            if response.status_code == 200:
                paper = response.json()

                result = {
                    "status": "success",
                    "paper": {
                        "id": paper.get('paperId'),
                        "title": paper.get('title'),
                        "authors": [author.get('name', 'Unknown') for author in paper.get('authors', [])],
                        "abstract": paper.get('abstract'),
                        "year": paper.get('year'),
                        "citation_count": paper.get('citationCount'),
                        "reference_count": paper.get('referenceCount'),
                        "fields_of_study": paper.get('fieldsOfStudy', []),
                        "url": paper.get('url'),
                        "venue": paper.get('venue'),
                        "publication_types": paper.get('publicationTypes', []),
                        "tldr": paper.get('tldr', {}).get('text', '') if paper.get('tldr') else ''
                    }
                }

                if include_references and 'references' in paper:
                    result['references'] = [
                        {
                            "title": ref.get('title', 'Unknown'),
                            "year": ref.get('year'),
                            "citation_count": ref.get('citationCount', 0)
                        } for ref in paper.get('references', [])[:10]
                    ]

                if include_citations and 'citations' in paper:
                    result['citations'] = [
                        {
                            "title": cit.get('title', 'Unknown'),
                            "year": cit.get('year'),
                            "citation_count": cit.get('citationCount', 0)
                        } for cit in paper.get('citations', [])[:10]
                    ]

                return result
            elif response.status_code == 429:
                # Rate limit exceeded
                return {
                    "status": "error",
                    "error": "Rate limit exceeded. Please wait before making another request.",
                    "retry_after": response.headers.get('Retry-After', '1 second')
                }
            elif response.status_code == 404:
                return {
                    "status": "error",
                    "error": f"Paper not found with ID: {paper_id}"
                }
            else:
                return {
                    "status": "error",
                    "error": f"Failed to fetch paper details: {response.status_code}",
                    "message": response.text[:200]  # Limit error message length
                }

        except requests.exceptions.ConnectionError:
            return {
                "status": "error",
                "error": "Connection error. Please check your internet connection."
            }
        except requests.exceptions.Timeout:
            return {
                "status": "error",
                "error": "Request timed out. Please try again."
            }
        except Exception as e:
            return {
                "status": "error",
                "error": f"Unexpected error: {str(e)}"
            }


# ============== ADVANCED RESEARCH TOOLS ==============

# Idea Generation Tool
class IdeaGenerationTool(BaseTool):
    def __init__(self):
        self.dataset_distillation_techniques = [
            "gradient matching", "distribution matching", "feature matching",
            "meta-learning", "neural architecture search", "knowledge distillation",
            "adversarial training", "contrastive learning", "self-supervised learning"
        ]
        self.application_domains = [
            "computer vision", "natural language processing", "speech recognition",
            "medical imaging", "autonomous driving", "robotics", "multimodal learning"
        ]
        self.innovation_patterns = [
            "combine_techniques", "apply_to_new_domain", "address_limitation",
            "theoretical_analysis", "empirical_study", "novel_architecture"
        ]

    @property
    def name(self) -> str:
        return "generate_research_ideas"

    @property
    def description(self) -> str:
        return "Generate novel research ideas for AI/ML research, particularly focused on dataset distillation and related areas"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "focus_area": {
                    "type": "string",
                    "description": "Primary research focus area (e.g., 'dataset distillation', 'few-shot learning')",
                    "default": "dataset distillation"
                },
                "innovation_level": {
                    "type": "string",
                    "enum": ["incremental", "moderate", "breakthrough"],
                    "description": "Desired level of innovation",
                    "default": "moderate"
                },
                "num_ideas": {
                    "type": "integer",
                    "description": "Number of ideas to generate",
                    "default": 3,
                    "minimum": 1,
                    "maximum": 10
                }
            },
            "required": ["focus_area"]
        }

    def execute(self, focus_area: str, innovation_level: str = "moderate", num_ideas: int = 3, **kwargs) -> dict:
        try:
            ideas = []
            for i in range(num_ideas):
                if innovation_level == "breakthrough":
                    idea = self._generate_breakthrough_idea(focus_area)
                elif innovation_level == "incremental":
                    idea = self._generate_incremental_idea(focus_area)
                else:
                    idea = self._generate_moderate_idea(focus_area)

                ideas.append({
                    "id": f"idea_{i+1}",
                    "title": idea["title"],
                    "description": idea["description"],
                    "innovation_level": innovation_level,
                    "focus_area": focus_area
                })

            return {
                "status": "success",
                "generated_ideas": ideas,
                "total_ideas": len(ideas)
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _generate_breakthrough_idea(self, focus_area: str) -> dict:
        technique1 = random.choice(self.dataset_distillation_techniques)
        technique2 = random.choice(self.dataset_distillation_techniques)
        domain = random.choice(self.application_domains)

        return {
            "title": f"Novel {technique1.title()}-{technique2.title()} Framework for {focus_area.title()}",
            "description": f"Develop a groundbreaking approach combining {technique1} with {technique2} for {focus_area} in {domain} applications, addressing fundamental limitations in current methods."
        }

    def _generate_moderate_idea(self, focus_area: str) -> dict:
        technique = random.choice(self.dataset_distillation_techniques)
        domain = random.choice(self.application_domains)

        return {
            "title": f"Enhanced {focus_area.title()} via {technique.title()}",
            "description": f"Improve {focus_area} performance by integrating {technique} techniques, specifically targeting {domain} applications."
        }

    def _generate_incremental_idea(self, focus_area: str) -> dict:
        technique = random.choice(self.dataset_distillation_techniques)

        return {
            "title": f"Optimized {technique.title()} for {focus_area.title()}",
            "description": f"Incremental improvements to existing {technique} methods in {focus_area} through hyperparameter optimization and architectural refinements."
        }

# Feasibility Analysis Tool
class FeasibilityAnalysisTool(BaseTool):
    @property
    def name(self) -> str:
        return "analyze_feasibility"

    @property
    def description(self) -> str:
        return "Analyze the technical and practical feasibility of research ideas"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "research_idea": {
                    "type": "string",
                    "description": "Description of the research idea to analyze"
                },
                "timeline": {
                    "type": "string",
                    "description": "Expected timeline for the research",
                    "default": "1 year"
                },
                "resources": {
                    "type": "object",
                    "description": "Available resources",
                    "default": {}
                }
            },
            "required": ["research_idea"]
        }

    def execute(self, research_idea: str, timeline: str = "1 year", resources: dict = None, **kwargs) -> dict:
        try:
            # Simple feasibility scoring based on keywords and complexity
            complexity_score = self._assess_complexity(research_idea)
            resource_score = self._assess_resources(resources or {})
            timeline_score = self._assess_timeline(timeline)

            overall_score = (complexity_score + resource_score + timeline_score) / 3

            feasibility_level = "High" if overall_score >= 0.7 else "Moderate" if overall_score >= 0.5 else "Low"

            return {
                "status": "success",
                "overall_feasibility_score": overall_score,
                "feasibility_level": feasibility_level,
                "complexity_score": complexity_score,
                "resource_score": resource_score,
                "timeline_score": timeline_score,
                "recommendations": self._generate_recommendations(overall_score, research_idea)
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _assess_complexity(self, idea: str) -> float:
        complex_keywords = ["novel", "breakthrough", "first", "new architecture", "theoretical"]
        simple_keywords = ["improve", "optimize", "extend", "apply", "incremental"]

        idea_lower = idea.lower()
        complexity = 0.5  # baseline

        for keyword in complex_keywords:
            if keyword in idea_lower:
                complexity -= 0.1

        for keyword in simple_keywords:
            if keyword in idea_lower:
                complexity += 0.1

        return max(0.1, min(1.0, complexity))

    def _assess_resources(self, resources: dict) -> float:
        # Simple resource assessment
        score = 0.5
        if resources.get("team_size", 1) >= 2:
            score += 0.2
        if resources.get("compute_budget") in ["high", "unlimited"]:
            score += 0.2
        if resources.get("expertise_level") == "expert":
            score += 0.1
        return min(1.0, score)

    def _assess_timeline(self, timeline: str) -> float:
        timeline_lower = timeline.lower()
        if "month" in timeline_lower:
            months = int(''.join(filter(str.isdigit, timeline)) or 6)
            return 0.9 if months >= 6 else 0.6
        elif "year" in timeline_lower:
            years = int(''.join(filter(str.isdigit, timeline)) or 1)
            return 0.8 if years >= 1 else 0.5
        return 0.7

    def _generate_recommendations(self, score: float, idea: str) -> list:
        recommendations = []
        if score < 0.5:
            recommendations.append("Consider simplifying the research scope")
            recommendations.append("Seek additional resources or collaborators")
        elif score < 0.7:
            recommendations.append("Plan for potential challenges and contingencies")
            recommendations.append("Consider phased implementation approach")
        else:
            recommendations.append("Research idea appears highly feasible")
            recommendations.append("Consider expanding scope for greater impact")
        return recommendations

# Critical Thinking Tool
class CriticalThinkingTool(BaseTool):
    @property
    def name(self) -> str:
        return "critical_evaluation"

    @property
    def description(self) -> str:
        return "Perform critical evaluation of research ideas and methodologies"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": "Research content to evaluate"
                },
                "evaluation_type": {
                    "type": "string",
                    "enum": ["methodology", "results", "idea_critique"],
                    "description": "Type of evaluation to perform",
                    "default": "methodology"
                }
            },
            "required": ["content"]
        }

    def execute(self, content: str, evaluation_type: str = "methodology", **kwargs) -> dict:
        try:
            evaluation = {
                "strengths": [],
                "weaknesses": [],
                "concerns": [],
                "recommendations": []
            }

            content_lower = content.lower()

            # Check for methodological strengths
            if "control" in content_lower:
                evaluation["strengths"].append("Includes control conditions")
            if "statistical" in content_lower:
                evaluation["strengths"].append("Statistical analysis mentioned")
            if "baseline" in content_lower:
                evaluation["strengths"].append("Baseline comparisons included")

            # Check for potential issues
            if "small sample" in content_lower:
                evaluation["concerns"].append("Small sample size may limit generalizability")
            if "single dataset" in content_lower:
                evaluation["concerns"].append("Single dataset evaluation may not be sufficient")

            # Generate recommendations
            if not evaluation["strengths"]:
                evaluation["recommendations"].append("Consider strengthening experimental design")
            if evaluation["concerns"]:
                evaluation["recommendations"].append("Address identified concerns before implementation")

            return {
                "status": "success",
                "evaluation_type": evaluation_type,
                "evaluation": evaluation
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

# Advanced Calculation Tool
class AdvancedCalculationTool(BaseTool):
    @property
    def name(self) -> str:
        return "advanced_calculate"

    @property
    def description(self) -> str:
        return "Perform advanced mathematical calculations and statistical analysis"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": ["statistical_analysis", "sample_size", "correlation"],
                    "description": "Type of calculation to perform"
                },
                "data": {
                    "type": "array",
                    "items": {"type": "number"},
                    "description": "Numerical data for analysis"
                },
                "parameters": {
                    "type": "object",
                    "description": "Additional parameters for the calculation"
                }
            },
            "required": ["operation"]
        }

    def execute(self, operation: str, data: list = None, parameters: dict = None, **kwargs) -> dict:
        try:
            if operation == "statistical_analysis" and data:
                import statistics
                return {
                    "status": "success",
                    "operation": operation,
                    "results": {
                        "mean": statistics.mean(data),
                        "median": statistics.median(data),
                        "std_dev": statistics.stdev(data) if len(data) > 1 else 0,
                        "min": min(data),
                        "max": max(data),
                        "count": len(data)
                    }
                }
            elif operation == "sample_size":
                # Simple sample size calculation
                effect_size = parameters.get("effect_size", 0.5) if parameters else 0.5
                alpha = parameters.get("alpha", 0.05) if parameters else 0.05
                power = parameters.get("power", 0.8) if parameters else 0.8

                # Simplified calculation
                sample_size = int(16 * (1.96 + 0.84)**2 / effect_size**2)

                return {
                    "status": "success",
                    "operation": operation,
                    "results": {
                        "required_sample_size": sample_size,
                        "effect_size": effect_size,
                        "alpha": alpha,
                        "power": power
                    }
                }
            else:
                return {
                    "status": "error",
                    "error": f"Operation '{operation}' not implemented or missing data"
                }
        except Exception as e:
            return {"status": "error", "error": str(e)}

# Literature Review Tool
class LiteratureReviewTool(BaseTool):
    @property
    def name(self) -> str:
        return "literature_review"

    @property
    def description(self) -> str:
        return "Perform automated literature review analysis and gap identification"

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "papers_data": {
                    "type": "array",
                    "description": "List of papers to analyze"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["gap_analysis", "trend_analysis", "thematic_review"],
                    "description": "Type of literature analysis",
                    "default": "gap_analysis"
                }
            },
            "required": ["papers_data"]
        }

    def execute(self, papers_data: list, analysis_type: str = "gap_analysis", **kwargs) -> dict:
        try:
            if analysis_type == "gap_analysis":
                gaps = self._identify_gaps(papers_data)
                return {
                    "status": "success",
                    "analysis_type": analysis_type,
                    "identified_gaps": gaps,
                    "papers_analyzed": len(papers_data)
                }
            elif analysis_type == "thematic_review":
                themes = self._extract_themes(papers_data)
                return {
                    "status": "success",
                    "analysis_type": analysis_type,
                    "themes": themes,
                    "papers_analyzed": len(papers_data)
                }
            else:
                return {
                    "status": "error",
                    "error": f"Analysis type '{analysis_type}' not implemented"
                }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _identify_gaps(self, papers_data: list) -> dict:
        # Simple gap identification based on paper content
        gaps = {
            "theoretical_gaps": [],
            "empirical_gaps": [],
            "methodological_gaps": []
        }

        # Analyze papers for common gap indicators
        for paper in papers_data:
            title = paper.get("title", "").lower()
            abstract = paper.get("abstract", "").lower()

            if "theoretical" not in abstract and "theory" not in abstract:
                gaps["theoretical_gaps"].append("Limited theoretical foundation")
            if "experiment" not in abstract and "evaluation" not in abstract:
                gaps["empirical_gaps"].append("Insufficient experimental validation")
            if "method" not in abstract and "approach" not in abstract:
                gaps["methodological_gaps"].append("Unclear methodology")

        return gaps

    def _extract_themes(self, papers_data: list) -> dict:
        themes = {}
        common_terms = ["learning", "neural", "deep", "machine", "algorithm", "model"]

        for paper in papers_data:
            title = paper.get("title", "").lower()
            for term in common_terms:
                if term in title:
                    themes[term] = themes.get(term, 0) + 1

        return themes

# ============== ENHANCED AGENT CLASS WITH ALL TOOLS ==============
class GeminiAgentWithTools:
    def __init__(self, config: dict = CONFIG, silent: bool = True):
        self.config = config
        self.model = genai.GenerativeModel(self.config['gemini']['model'])
        self.silent = silent
        self.tools = self._initialize_tools()

    def count_tokens(self, text: str) -> int:
        """Counts the number of tokens in a given text."""
        try:
            return self.model.count_tokens(text).total_tokens
        except Exception:
            # Fallback if API fails: estimate based on character count
            return len(text) // 4

    def _initialize_tools(self):
        """Initialize all available tools"""
        tools = {}

        # Add Local Knowledge Base tools
        try:
            local_kb_tool = LocalKnowledgeBaseTool()
            tools[local_kb_tool.name] = local_kb_tool

            read_doc_tool = ReadDocumentTool()
            tools[read_doc_tool.name] = read_doc_tool

            if not self.silent:
                print(f"✅ Local knowledge base tools loaded ({len(local_kb_tool.documents)} documents)")
        except Exception as e:
            if not self.silent:
                print(f"⚠️ Could not load local knowledge base: {e}")

        # Add Semantic Scholar tools
        semantic_tool = SemanticScholarTool()
        tools[semantic_tool.name] = semantic_tool

        paper_tool = PaperDetailsTool()
        tools[paper_tool.name] = paper_tool

        # Add Advanced Research Tools
        idea_tool = IdeaGenerationTool()
        tools[idea_tool.name] = idea_tool

        feasibility_tool = FeasibilityAnalysisTool()
        tools[feasibility_tool.name] = feasibility_tool

        critical_tool = CriticalThinkingTool()
        tools[critical_tool.name] = critical_tool

        calc_tool = AdvancedCalculationTool()
        tools[calc_tool.name] = calc_tool

        lit_review_tool = LiteratureReviewTool()
        tools[lit_review_tool.name] = lit_review_tool

        if not self.silent:
            print(f"✅ Loaded {len(tools)} research tools")

        return tools

    def _print(self, message: str):
        if not self.silent:
            print(message)

    def _format_tools_for_prompt(self) -> str:
        """Format tool descriptions for the prompt"""
        tool_desc = "You have access to the following research tools:\n\n"

        for tool_name, tool in self.tools.items():
            tool_desc += f"**{tool_name}**: {tool.description}\n"
            params = tool.parameters.get('properties', {})
            if params:
                tool_desc += "  Parameters:\n"
                for param_name, param_info in params.items():
                    required = param_name in tool.parameters.get('required', [])
                    req_str = " (required)" if required else " (optional)"
                    tool_desc += f"    - {param_name}: {param_info.get('type', 'any')} - {param_info.get('description', 'No description')}{req_str}\n"
            tool_desc += "\n"

        tool_desc += """To use a tool, respond with:
TOOL_CALL: {"name": "tool_name", "arguments": {"param1": "value1", "param2": "value2"}}

Important:
- First search your local knowledge base using 'search_local_knowledge' for relevant information
- Then search external papers using 'search_research_papers' for additional context
- Use 'read_full_document' to get complete content from specific local papers
- Use 'get_paper_details' for detailed information about external papers

After receiving tool results, analyze them and continue with your response.
"""
        return tool_desc

    def _execute_tool(self, tool_name: str, arguments: dict) -> dict:
        """Execute a tool with given arguments"""
        if tool_name in self.tools:
            try:
                result = self.tools[tool_name].execute(**arguments)

                # Handle rate limit errors with retry
                if (result.get('status') == 'error' and
                    'rate limit' in result.get('error', '').lower()):
                    self._print(f"⏳ Rate limit hit, waiting 2 seconds before retry...")
                    time.sleep(2)
                    # Retry once
                    result = self.tools[tool_name].execute(**arguments)

                return result
            except Exception as e:
                error_msg = str(e)
                self._print(f"❌ Tool execution error: {error_msg}")

                # If it's a connection error, provide more context
                if "connection" in error_msg.lower():
                    return {
                        "status": "error",
                        "error": f"Connection error: {error_msg}. Check your internet connection."
                    }

                return {"status": "error", "error": error_msg}
        else:
            available_tools = list(self.tools.keys())
            return {
                "status": "error",
                "error": f"Tool '{tool_name}' not found. Available tools: {available_tools}"
            }


    def _extract_tool_calls(self, text: str) -> List[Dict]:
        """Extract all tool calls from the response"""
        tool_calls = []
        parts = text.split("TOOL_CALL:")

        for i in range(1, len(parts)):
            try:
                json_str = parts[i].strip()
                # Find the JSON object
                start = json_str.find("{")
                end = json_str.find("}", start)
                if start != -1 and end != -1:
                    # Find the actual end of the JSON object
                    bracket_count = 1
                    j = start + 1
                    while j < len(json_str) and bracket_count > 0:
                        if json_str[j] == '{':
                            bracket_count += 1
                        elif json_str[j] == '}':
                            bracket_count -= 1
                        j += 1

                    json_obj = json_str[start:j]
                    tool_call = json.loads(json_obj)
                    tool_calls.append(tool_call)
            except:
                continue

        return tool_calls

    def chat(self, messages: List[Dict[str, str]], use_tools: bool = True) -> str:
        prompt_parts = []

        # Add tool descriptions if tools are enabled
        if use_tools and self.tools:
            prompt_parts.append(self._format_tools_for_prompt())

        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")

        prompt = "\n\n".join(prompt_parts)

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            self._print(f"Error in Gemini API call: {e}")
            return f"Error: {str(e)}"

    def run_task_with_tools(self, task: str) -> Dict[str, Any]:
        """Run a task with tool support"""
        system_prompt = """You are a helpful AI assistant with access to both a local knowledge base of research papers and external academic research tools.

        When answering questions:
        1. First search the local knowledge base for relevant information
        2. Then search external sources for additional context
        3. Read full documents when you need detailed information
        4. Always cite sources properly (both local and external)
        5. Provide evidence-based, comprehensive answers

        Think step by step and use multiple tools to gather comprehensive information."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": task}
        ]

        max_iterations = self.config['agent']['max_iterations']

        for i in range(max_iterations):
            self._print(f"\n🔄 Iteration {i+1}/{max_iterations}")

            # Get response from Gemini
            response = self.chat(messages, use_tools=True)
            self._print(f"Assistant: {response[:200]}..." if len(response) > 200 else f"Assistant: {response}")

            # Check for tool calls
            tool_calls = self._extract_tool_calls(response)

            if tool_calls:
                # Execute all tool calls
                tool_results = []
                for tool_call in tool_calls:
                    tool_name = tool_call.get('name')
                    arguments = tool_call.get('arguments', {})

                    self._print(f"🔧 Executing tool: {tool_name}")
                    result = self._execute_tool(tool_name, arguments)
                    tool_results.append({
                        "tool": tool_name,
                        "arguments": arguments,
                        "result": result
                    })

                # Add tool results to conversation
                tool_results_text = "Tool execution results:\n"
                for tr in tool_results:
                    tool_results_text += f"\nTool: {tr['tool']}\n"
                    tool_results_text += f"Result: {json.dumps(tr['result'], indent=2)}\n"

                messages.append({"role": "assistant", "content": response})
                messages.append({"role": "user", "content": tool_results_text + "\n\nPlease continue with your analysis based on these results."})
            else:
                # No more tool calls, task is complete
                return {"status": "completed", "result": response}

        return {"status": "max_iterations_reached", "result": messages[-1]['content']}
# ============== CHECKPOINT MANAGEMENT ==============
class CheckpointManager:
    def __init__(self, checkpoint_dir: str = "/content/drive/MyDrive/research_checkpoints"):
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)

    def save_checkpoint(self, session_id: str, data: dict):
        """Save checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            with open(checkpoint_file, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"💾 Checkpoint saved: {checkpoint_file}")
        except Exception as e:
            print(f"❌ Failed to save checkpoint: {e}")

    def load_checkpoint(self, session_id: str) -> dict:
        """Load checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            if os.path.exists(checkpoint_file):
                with open(checkpoint_file, 'r') as f:
                    data = json.load(f)
                print(f"📂 Checkpoint loaded: {checkpoint_file}")
                return data
        except Exception as e:
            print(f"❌ Failed to load checkpoint: {e}")
        return {}

    def list_checkpoints(self) -> List[str]:
        """List available checkpoints"""
        try:
            files = glob.glob(os.path.join(self.checkpoint_dir, "*_checkpoint.json"))
            return [os.path.basename(f).replace("_checkpoint.json", "") for f in files]
        except:
            return []

# ============== ENHANCED ORCHESTRATOR WITH ALL TOOLS ==============
class GeminiOrchestratorWithTools:
    def __init__(self, config: dict = CONFIG):
        self.agent = GeminiAgentWithTools(config, silent=True)
        self.config = config
        self.num_agents = self.config['orchestrator']['parallel_agents']
        self.max_concurrent = self.config['orchestrator']['max_concurrent']
        self.batch_delay = self.config['orchestrator']['batch_delay']
        self.checkpoint_manager = CheckpointManager()
        self.session_id = None

    def set_session_id(self, session_id: str):
        """Set session ID for checkpoint management"""
        self.session_id = session_id

    def generate_questions(self, query: str) -> List[str]:
        """Generate specialized questions including local knowledge and research-focused ones"""

        # For large numbers of agents, we need a more sophisticated approach
        if self.num_agents > 10:
            # Define categories of analysis with specific question templates
            question_categories = {
                "local_knowledge_base": [
                    f"What foundational papers about {query} exist in our local knowledge base?",
                    f"Which local papers provide mathematical frameworks for {query}?",
                    f"What implementation details for {query} are documented in our knowledge base?",
                    f"Which local papers discuss limitations or challenges of {query}?",
                    f"What experimental results for {query} are available in our collection?"
                ],
                "semantic_scholar_search": [
                    f"What are the most cited recent papers (2023-2024) about {query} in external databases?",
                    f"Which breakthrough papers on {query} have emerged in the last 6 months?",
                    f"What alternative approaches to {query} are being explored in current literature?",
                    f"Which research groups are leading the development of {query}?",
                    f"What papers cite the seminal works on {query}?"
                ],
                "novelty_assessment": [
                    f"What gaps exist in current {query} research that could be addressed?",
                    f"How can {query} be extended to multimodal scenarios?",
                    f"What novel combinations of {query} with other techniques haven't been explored?",
                    f"Which aspects of {query} lack theoretical justification?",
                    f"What real-world applications of {query} remain unexplored?"
                ],
                "technical_verification": [
                    f"Are the mathematical formulations in {query} papers consistent and correct?",
                    f"What are the computational complexity and efficiency considerations for {query}?",
                    f"How do different implementations of {query} compare in terms of performance?",
                    f"What are the key hyperparameters and their effects in {query} methods?",
                    f"Are the reported results for {query} reproducible based on paper descriptions?"
                ],
                "comparative_analysis": [
                    f"How does {query} compare to traditional baseline methods?",
                    f"What are the trade-offs between different {query} approaches?",
                    f"Which {query} method performs best under different conditions?",
                    f"How do local KB papers and external papers differ in their approach to {query}?",
                    f"What contradictions exist between different papers about {query}?"
                ],
                "implementation_details": [
                    f"What code repositories implement {query} and how complete are they?",
                    f"What are the critical implementation details often omitted in {query} papers?",
                    f"How can {query} be efficiently implemented for large-scale applications?",
                    f"What software dependencies and frameworks are required for {query}?",
                    f"Are there any implementation tricks or optimizations for {query}?"
                ],
                "theoretical_foundations": [
                    f"What mathematical principles underpin {query}?",
                    f"Are there theoretical guarantees or proofs for {query} methods?",
                    f"How does {query} relate to fundamental concepts in machine learning?",
                    f"What assumptions do {query} methods make and are they valid?",
                    f"Can {query} be unified under a more general theoretical framework?"
                ],
                "future_directions": [
                    f"What are the most promising future research directions for {query}?",
                    f"How might {query} evolve with advances in AI/ML infrastructure?",
                    f"What interdisciplinary applications could benefit from {query}?",
                    f"What would be the next logical step in advancing {query}?",
                    f"How can {query} be adapted for emerging computing paradigms?"
                ]
            }

            # Generate questions ensuring diversity
            all_questions = []
            questions_per_category = max(2, self.num_agents // len(question_categories))

            for category, template_questions in question_categories.items():
                # Use templates and also generate custom questions
                all_questions.extend(template_questions[:questions_per_category])

            # If we need more questions, generate custom ones
            if len(all_questions) < self.num_agents:
                additional_needed = self.num_agents - len(all_questions)

                prompt = f"""Generate {additional_needed} highly specific, technical research questions about: {query}

    Focus on:
    - Specific mathematical formulations and their correctness
    - Implementation challenges and solutions
    - Novel extensions and applications
    - Experimental validation and reproducibility
    - Theoretical gaps and opportunities
    - Cross-referencing local knowledge base with external literature

    Make questions specific and actionable for research purposes.

    Return ONLY a JSON array of {additional_needed} questions."""

                messages = [
                    {"role": "system", "content": "You are an expert research question generator for AI/ML research."},
                    {"role": "user", "content": prompt}
                ]

                response = self.agent.chat(messages, use_tools=False)

                try:
                    start = response.find("[")
                    end = response.rfind("]") + 1
                    if start != -1 and end != 0:
                        custom_questions = json.loads(response[start:end])
                        all_questions.extend(custom_questions)
                except:
                    # Add more specific fallback questions
                    for i in range(additional_needed):
                        all_questions.append(f"Investigate specific aspect {i+1} of {query} using all available resources")

            # Shuffle to mix different types of questions
            import random
            random.shuffle(all_questions)

            return all_questions[:self.num_agents]

        else:
            # For smaller number of agents, use a more focused prompt
            prompt = f"""Generate exactly {self.num_agents} diverse, technical research questions about: {query}

    Each question should focus on ONE of these aspects:
    1. Local knowledge base exploration (existing papers in collection)
    2. External literature search (Semantic Scholar)
    3. Novel research opportunities and gaps
    4. Technical verification (math, calculations, implementations)
    5. Comparative analysis across sources
    6. Implementation and reproducibility
    7. Theoretical foundations and proofs
    8. Future research directions

    Make questions specific, technical, and actionable for academic research.

    Return ONLY a JSON array of exactly {self.num_agents} questions.
    Example: ["What mathematical formulations for X exist in local papers?", "How do recent 2024 papers extend X?", "What novel applications of X remain unexplored?", "Are the computational complexity claims for X verified?"]"""

            messages = [
                {"role": "system", "content": "You are an expert research question generator specializing in AI/ML research analysis."},
                {"role": "user", "content": prompt}
            ]

            response = self.agent.chat(messages, use_tools=False)

            try:
                start = response.find("[")
                end = response.rfind("]") + 1
                if start != -1 and end != 0:
                    questions = json.loads(response[start:end])
                    if len(questions) >= self.num_agents:
                        return questions[:self.num_agents]
                    else:
                        # Pad with specific questions if not enough
                        while len(questions) < self.num_agents:
                            category_idx = len(questions) % 8
                            categories = [
                                f"Search local KB for mathematical foundations of {query}",
                                f"Find recent 2024 papers about {query} improvements",
                                f"Identify novel applications of {query} not yet explored",
                                f"Verify calculations and complexity analysis in {query} papers",
                                f"Compare {query} implementations across different papers",
                                f"Analyze reproducibility of {query} results",
                                f"Examine theoretical guarantees for {query}",
                                f"Propose future extensions of {query}"
                            ]
                            questions.append(categories[category_idx])
                        return questions[:self.num_agents]
            except Exception as e:
                print(f"Error parsing questions: {e}")

            # Comprehensive fallback questions
            fallback_questions = [
                f"What foundational papers about {query} exist in the local knowledge base?",
                f"What are the most recent and highly-cited papers about {query} from Semantic Scholar?",
                f"What novel research directions for {query} haven't been explored yet?",
                f"Are the mathematical formulations and calculations in {query} papers correct and consistent?",
                f"How do different implementations of {query} compare in terms of efficiency and accuracy?",
                f"What implementation details for {query} are missing from published papers?",
                f"What theoretical foundations and proofs exist for {query} methods?",
                f"How can {query} be extended or improved based on current limitations?",
                f"What datasets and benchmarks are used to evaluate {query}?",
                f"Which research groups are actively working on {query} and what are their contributions?",
                f"What are the computational requirements and scalability issues of {query}?",
                f"How does {query} relate to other methods in the field?",
                f"What are the failure cases and limitations of current {query} approaches?",
                f"What preprocessing or setup is required for {query} methods?",
                f"Are there any contradictions between different papers about {query}?",
                f"What hyperparameter settings are crucial for {query} performance?",
                f"How can {query} be adapted for multimodal scenarios?",
                f"What are the ethical considerations and biases in {query} applications?",
                f"What open-source implementations of {query} are available and how complete are they?",
                f"What are the next logical steps in advancing {query} research?"
            ]

            # Return the required number of questions
            return fallback_questions[:self.num_agents]


    def run_agent_task(self, agent_id: int, question: str, timeout: int = 600) -> Dict[str, Any]:
        """Run a single agent task with all tools and timeout handling"""
        agent = GeminiAgentWithTools(self.config, silent=True)
        start_time = time.time()

        try:
            # Apply rate limiting for Semantic Scholar
            s2_rate_limiter.wait()

            result = agent.run_task_with_tools(question)
            duration = time.time() - start_time

            # Check for timeout
            if duration > timeout:
                return {
                    "agent_id": agent_id,
                    "question": question,
                    "response": f"Agent {agent_id} timed out after {timeout} seconds",
                    "status": "timeout",
                    "duration": duration
                }

            return {
                "agent_id": agent_id,
                "question": question,
                "response": result.get("result", "No response"),
                "status": result.get("status", "completed"),
                "duration": duration,
                "tools_used": getattr(result, 'tools_used', [])
            }
        except Exception as e:
            return {
                "agent_id": agent_id,
                "question": question,
                "response": f"Error: {str(e)}",
                "status": "error",
                "duration": time.time() - start_time
            }

    def run_sequential_batch_orchestration(self, query: str, progress_callback=None) -> Dict[str, Any]:
        """Run large-scale sequential batch orchestration with checkpointing"""

        if not self.session_id:
            self.session_id = f"session_{int(time.time())}"

        print(f"🚀 Starting {self.num_agents}-agent orchestration for: {query}")
        print(f"📊 Configuration: {self.max_concurrent} concurrent, {self.batch_delay}s batch delay")

        # Check for existing checkpoint
        checkpoint_data = self.checkpoint_manager.load_checkpoint(self.session_id)

        if checkpoint_data:
            print(f"📂 Resuming from checkpoint with {len(checkpoint_data.get('completed_agents', []))} completed agents")
            completed_agents = checkpoint_data.get('completed_agents', [])
            agent_results = checkpoint_data.get('agent_results', [])
            questions = checkpoint_data.get('questions', [])
        else:
            print("🔄 Generating research questions...")
            questions = self.generate_questions(query)
            completed_agents = []
            agent_results = []

            # Save initial checkpoint
            self.checkpoint_manager.save_checkpoint(self.session_id, {
                'query': query,
                'questions': questions,
                'completed_agents': completed_agents,
                'agent_results': agent_results,
                'total_agents': self.num_agents
            })

        # Calculate batches
        remaining_agents = [i for i in range(self.num_agents) if i not in completed_agents]
        total_batches = (len(remaining_agents) + self.max_concurrent - 1) // self.max_concurrent

        print(f"📋 Processing {len(remaining_agents)} remaining agents in {total_batches} batches")

        # Process agents in batches
        for batch_idx in range(total_batches):
            batch_start = batch_idx * self.max_concurrent
            batch_end = min(batch_start + self.max_concurrent, len(remaining_agents))
            batch_agents = remaining_agents[batch_start:batch_end]

            print(f"\n🔄 Batch {batch_idx + 1}/{total_batches}: Processing agents {batch_agents}")

            # Run batch concurrently
            batch_results = []
            with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
                future_to_agent = {
                    executor.submit(self.run_agent_task, agent_id, questions[agent_id], 600): agent_id
                    for agent_id in batch_agents
                }

                for future in as_completed(future_to_agent, timeout=700):
                    try:
                        result = future.result()
                        batch_results.append(result)
                        agent_id = result['agent_id']
                        completed_agents.append(agent_id)

                        print(f"  ✅ Agent {agent_id} completed ({result['status']}) - {result['duration']:.1f}s")

                        # Update progress
                        if progress_callback:
                            progress_callback(len(completed_agents), self.num_agents, result)

                    except Exception as e:
                        agent_id = future_to_agent[future]
                        error_result = {
                            "agent_id": agent_id,
                            "question": questions[agent_id],
                            "response": f"Batch execution error: {str(e)}",
                            "status": "error",
                            "duration": 600
                        }
                        batch_results.append(error_result)
                        completed_agents.append(agent_id)
                        print(f"  ❌ Agent {agent_id} failed: {str(e)}")

            # Add batch results to overall results
            agent_results.extend(batch_results)

            # Save checkpoint after each batch
            checkpoint_data = {
                'query': query,
                'questions': questions,
                'completed_agents': completed_agents,
                'agent_results': agent_results,
                'total_agents': self.num_agents,
                'batch_completed': batch_idx + 1,
                'total_batches': total_batches
            }
            self.checkpoint_manager.save_checkpoint(self.session_id, checkpoint_data)

            # Batch delay (except for last batch)
            if batch_idx < total_batches - 1:
                print(f"⏳ Waiting {self.batch_delay}s before next batch...")
                time.sleep(self.batch_delay)

        print(f"\n🎉 All {self.num_agents} agents completed!")
        print(f"📊 Success: {len([r for r in agent_results if r['status'] == 'completed'])}")
        print(f"❌ Errors: {len([r for r in agent_results if r['status'] == 'error'])}")
        print(f"⏰ Timeouts: {len([r for r in agent_results if r['status'] == 'timeout'])}")

        # Synthesize results
        print("🔀 Synthesizing results...")
        final_synthesis = self.synthesize_responses(query, agent_results)

        # Save final results
        final_data = checkpoint_data.copy()
        final_data['final_synthesis'] = final_synthesis
        final_data['completion_time'] = datetime.now().isoformat()
        self.checkpoint_manager.save_checkpoint(f"{self.session_id}_final", final_data)

        return {
            'query': query,
            'total_agents': self.num_agents,
            'agent_results': agent_results,
            'final_synthesis': final_synthesis,
            'session_id': self.session_id,
            'statistics': {
                'completed': len([r for r in agent_results if r['status'] == 'completed']),
                'errors': len([r for r in agent_results if r['status'] == 'error']),
                'timeouts': len([r for r in agent_results if r['status'] == 'timeout']),
                'total_duration': sum(r['duration'] for r in agent_results),
                'avg_duration': sum(r['duration'] for r in agent_results) / len(agent_results)
            }
        }

# In the GeminiOrchestratorWithTools class

    def synthesize_responses(self, query: str, agent_results: List[Dict]) -> str:
        """
        Synthesize responses using a recursive map-reduce strategy to handle
        arbitrarily large content and avoid context window errors.
        """
        SAFE_CONTEXT_LIMIT = 1000000
        print(f"🔀 Starting recursive synthesis for {len(agent_results)} agents...")
        texts_to_process = [f"Agent {r['agent_id']} (Question: {r['question']}):\n{r['response']}" for r in agent_results]

        summarization_level = 1
        while True:
            total_tokens = sum(self.agent.count_tokens(text) for text in texts_to_process)
            print(f"   [Level {summarization_level}] Processing {len(texts_to_process)} text sections with a total of {total_tokens} tokens.")
            if total_tokens <= SAFE_CONTEXT_LIMIT:
                print("   ...content is now small enough for final synthesis.")
                break

            print(f"   ...total tokens exceed the safe limit of {SAFE_CONTEXT_LIMIT}. Summarizing in batches.")
            groups = self._create_token_aware_groups(texts_to_process, SAFE_CONTEXT_LIMIT)

            new_summaries = []
            for i, group in enumerate(groups):
                print(f"      - Summarizing batch {i+1}/{len(groups)}...")
                summary = self._direct_synthesis(query, group)
                if "Error:" in summary:
                    print(f"      ⚠️ Warning: Synthesis for batch {i+1} failed. Skipping.")
                    continue
                new_summaries.append(summary)

            texts_to_process = new_summaries
            summarization_level += 1

        print("   ...performing final synthesis.")
        # FIX for f-string syntax error
        final_reports = "\n\n--- // ---\n\n".join(texts_to_process)
        final_prompt = f"""You have analyzed a research query in several stages.

Original Query: {query}

The findings were recursively summarized. Now, your final task is to synthesize these last reports into a single, cohesive, and comprehensive answer.

Final Reports:
{final_reports}

Provide the final, unified, evidence-based response:"""

        messages = [
            {"role": "system", "content": "You are an expert synthesis AI that combines multiple, pre-summarized reports into a single, final answer."},
            {"role": "user", "content": final_prompt}
        ]

        return self.agent.chat(messages, use_tools=False)


    def _create_token_aware_groups(self, texts: List[str], limit: int) -> List[List[str]]:
        """Groups texts into batches that do not exceed a token limit."""
        groups, current_group, current_tokens = [], [], 0
        for text in texts:
            text_tokens = self.agent.count_tokens(text)
            if current_tokens + text_tokens > limit:
                groups.append(current_group)
                current_group, current_tokens = [text], text_tokens
            else:
                current_group.append(text)
                current_tokens += text_tokens
        if current_group:
            groups.append(current_group)
        return groups

    def _direct_synthesis(self, query: str, text_group: List[str]) -> str:
        """Helper method to run a single synthesis call on a group of texts."""
        responses_text = "\n\n--- // ---\n\n".join(text_group)
        system_prompt = "You are a research assistant. Concisely summarize the key findings from the provided text sections into a single, structured report."
        synthesis_prompt = f"""Based on the original query '{query}', synthesize the following information into a single, coherent report.

Information:
{responses_text}

Synthesized Report:"""
        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": synthesis_prompt}]
        return self.agent.chat(messages, use_tools=False)

    def orchestrate(self, query: str, progress_callback=None) -> str:
        """Orchestrate multiple agents with checkpoint save/resume capability"""
        import pickle
        import os
        from datetime import datetime

        # Setup checkpoint file
        checkpoint_dir = "/content/drive/MyDrive/orchestrator_checkpoints"
        os.makedirs(checkpoint_dir, exist_ok=True)

        # Create unique checkpoint filename based on query
        query_hash = str(hash(query))[:10]
        checkpoint_file = f"{checkpoint_dir}/checkpoint_{query_hash}_{self.num_agents}agents.pkl"

        # Try to resume from checkpoint
        start_from_batch = 0
        agent_results = []
        questions = None
        completed_agent_ids = set()

        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, 'rb') as f:
                    checkpoint = pickle.load(f)
                    agent_results = checkpoint.get('agent_results', [])
                    questions = checkpoint.get('questions', None)
                    completed_agent_ids = set(checkpoint.get('completed_agent_ids', []))
                    start_from_batch = checkpoint.get('last_completed_batch', 0)

                    if progress_callback:
                        progress_callback(f"📂 RESUMING from checkpoint: {len(agent_results)} agents already completed")
                        progress_callback(f"   Completed agents: {sorted(completed_agent_ids)}")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"⚠️ Could not load checkpoint: {e}. Starting fresh.")

        # Generate questions only if not loaded from checkpoint
        if questions is None:
            if progress_callback:
                progress_callback(f"🎯 Generating {self.num_agents} specialized questions for local and external research...")
            questions = self.generate_questions(query)

            # if progress_callback:
            #     progress_callback(f"📋 Generated {len(questions)} research questions")
            #     for i, q in enumerate(questions[:10]):
            #         progress_callback(f"  Agent {i+1}: {q}")
            #     if len(questions) > 10:
            #         progress_callback(f"  ... and {len(questions) - 10} more questions")

            if progress_callback:
                progress_callback(f"📋 Generated {len(questions)} research questions. Displaying full list:")
                progress_callback("="*80)
                for i, q in enumerate(questions):
                    # This now loops through ALL questions
                    progress_callback(f"  Agent {i+1}: {q}")
                progress_callback("="*80)

        # Run agents in batches
        if progress_callback:
            progress_callback(f"\n🔄 Running {self.num_agents} agents with local KB and research tools...")
            if start_from_batch > 0:
                progress_callback(f"   Starting from batch {start_from_batch + 1}")

        # max_concurrent = min(self.config['orchestrator'].get('max_concurrent', 2), 2)
        max_concurrent = self.config['orchestrator'].get('max_concurrent', 1)
        batch_delay = self.config['orchestrator'].get('batch_delay', 2)

        # Calculate total batches
        total_batches = (self.num_agents + max_concurrent - 1) // max_concurrent

        # Process remaining batches
        batch_num = start_from_batch
        for batch_start_idx in range(start_from_batch * max_concurrent, self.num_agents, max_concurrent):
            batch_num += 1
            batch_end_idx = min(batch_start_idx + max_concurrent, self.num_agents)

            # Skip already completed agents
            batch_agent_ids = list(range(batch_start_idx + 1, batch_end_idx + 1))
            agents_to_run = [aid for aid in batch_agent_ids if aid not in completed_agent_ids]

            if not agents_to_run:
                if progress_callback:
                    progress_callback(f"\n📦 Batch {batch_num}/{total_batches} already completed, skipping...")
                continue

            if progress_callback:
                progress_callback(f"\n📦 Processing batch {batch_num}/{total_batches} (Agents {agents_to_run})...")

            with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                futures = {}
                for agent_id in agents_to_run:
                    question = questions[agent_id - 1]
                    future = executor.submit(self.run_agent_task, agent_id, question)
                    futures[future] = agent_id

                    # Small delay between starting agents
                    if agent_id != agents_to_run[-1]:
                        time.sleep(0.5)

                # Collect results as they complete
                for future in as_completed(futures):
                    agent_id = futures[future]
                    try:
                        result = future.result()
                        agent_results.append(result)
                        completed_agent_ids.add(agent_id)

                        if progress_callback:
                            status_emoji = "✅" if result['status'] != 'error' else "❌"
                            progress_callback(f"{status_emoji} Agent {agent_id} completed in {result['duration']:.2f}s")

                        # SAVE CHECKPOINT AFTER EACH AGENT COMPLETES
                        checkpoint_data = {
                            'agent_results': agent_results,
                            'questions': questions,
                            'completed_agent_ids': list(completed_agent_ids),
                            'last_completed_batch': batch_num - 1,
                            'total_agents': self.num_agents,
                            'query': query,
                            'timestamp': datetime.now().isoformat(),
                            'progress': f"{len(completed_agent_ids)}/{self.num_agents} agents completed"
                        }

                        with open(checkpoint_file, 'wb') as f:
                            pickle.dump(checkpoint_data, f)

                        # Also save a backup with timestamp
                        if len(completed_agent_ids) % 10 == 0:  # Every 10 agents
                            backup_file = f"{checkpoint_dir}/backup_{query_hash}_{len(completed_agent_ids)}agents_{datetime.now().strftime('%H%M%S')}.pkl"
                            with open(backup_file, 'wb') as f:
                                pickle.dump(checkpoint_data, f)
                            if progress_callback:
                                progress_callback(f"💾 Backup checkpoint saved: {len(completed_agent_ids)} agents")

                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"❌ Agent {agent_id} failed with error: {str(e)}")

            # Wait between batches to respect rate limits
            if batch_end_idx < self.num_agents:
                if progress_callback:
                    progress_callback(f"⏳ Waiting {batch_delay} seconds before next batch (rate limit)...")
                    progress_callback(f"💾 Progress saved: {len(completed_agent_ids)}/{self.num_agents} agents")
                time.sleep(batch_delay)

        # Sort results by agent_id to maintain order
        agent_results.sort(key=lambda x: x['agent_id'])

        # Synthesize all responses
        if progress_callback:
            progress_callback(f"\n🔀 Synthesizing responses from {len(agent_results)} agents...")

        final_response = self.synthesize_responses(query, agent_results)

        # Save final result
        # final_result_file = f"{checkpoint_dir}/final_result_{query_hash}_{self.num_agents}agents.txt"
        # with open(final_result_file, 'w', encoding='utf-8') as f:
        #     f.write(f"Query: {query}\n")
        #     f.write(f"Agents: {self.num_agents}\n")
        #     f.write(f"Timestamp: {datetime.now()}\n")
        #     f.write(f"\n{'='*80}\n\n")
        #     f.write(final_response)


        # Save final result with all questions included
        final_result_file = f"{checkpoint_dir}/final_result_{query_hash}_{self.num_agents}agents.txt"
        with open(final_result_file, 'w', encoding='utf-8') as f:
            f.write(f"Query: {query}\n")
            f.write(f"Agents: {self.num_agents}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")

            # Add the full list of agent questions to the report
            f.write("\n" + "="*80 + "\n")
            f.write("AGENT QUESTIONS\n")
            f.write("="*80 + "\n\n")
            for i, q in enumerate(questions):
                f.write(f"Agent {i+1}: {q}\n")
            f.write("\n")

            # Add the final synthesized response
            f.write("="*80 + "\n")
            f.write("FINAL SYNTHESIZED RESPONSE\n")
            f.write("="*80 + "\n\n")
            f.write(final_response)

        if progress_callback:
            progress_callback(f"📄 Final result saved to: {final_result_file}")
            progress_callback("\n✨ Analysis complete!")

        # Optionally clean up checkpoint file after successful completion
        # os.remove(checkpoint_file)

        return final_response


# ============== SIMPLE ORCHESTRATOR CLASS ==============
class GeminiOrchestratorSimple:
    def __init__(self, config: dict = CONFIG):
        self.agent = GeminiAgentWithTools(config, silent=True)
        self.config = config
        self.num_agents = self.config['orchestrator']['parallel_agents']

    def generate_questions(self, query: str) -> List[str]:
        prompt = f"""Generate {self.num_agents} different analytical questions to explore the following query from multiple perspectives:

    Query: {query}

    Return ONLY a JSON array of {self.num_agents} questions. Each question should focus on a different aspect.
    Example format: ["Question 1?", "Question 2?", "Question 3?", "Question 4?"]"""

        messages = [
            {"role": "system", "content": "You are a question generation AI."},
            {"role": "user", "content": prompt}
        ]

        response = self.agent.chat(messages, use_tools=False)

        try:
            start = response.find("[")
            end = response.rfind("]") + 1
            if start != -1 and end != 0:
                questions = json.loads(response[start:end])
                return questions[:self.num_agents]
        except:
            pass

        return [
            f"What are the key facts and background information about: {query}?",
            f"What are the main challenges or considerations regarding: {query}?",
            f"What are the potential solutions or approaches for: {query}?",
            f"What are the future implications or trends related to: {query}?"
        ][:self.num_agents]

    def run_agent_task(self, agent_id: int, question: str) -> Dict[str, Any]:
        agent = GeminiAgentWithTools(self.config, silent=True)
        start_time = time.time()

        try:
            messages = [
                {"role": "system", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": question}
            ]
            result = agent.chat(messages, use_tools=False)
            return {
                "agent_id": agent_id,
                "question": question,
                "response": result,
                "status": "completed",
                "duration": time.time() - start_time
            }
        except Exception as e:
            return {
                "agent_id": agent_id,
                "question": question,
                "response": f"Error: {str(e)}",
                "status": "error",
                "duration": time.time() - start_time
            }

    # def synthesize_responses(self, query: str, agent_results: List[Dict]) -> str:
    #     responses_text = "\n\n".join([
    #         f"Agent {r['agent_id']} (Question: {r['question']}):\n{r['response']}"
    #         for r in agent_results
    #     ])

    #     synthesis_prompt = f"""You have {len(agent_results)} different AI agents that analyzed the following query from different perspectives:

    # Original Query: {query}

    # Here are their responses:

    # {responses_text}

    # Synthesize all these perspectives into one comprehensive answer."""

    #     messages = [
    #         {"role": "system", "content": "You are a synthesis AI."},
    #         {"role": "user", "content": synthesis_prompt}
    #     ]

    #     return self.agent.chat(messages, use_tools=False)
    # Below new

    def synthesize_responses(self, query: str, agent_results: List[Dict]) -> str:
        """
        Synthesize responses using a recursive map-reduce strategy to handle
        arbitrarily large content and avoid context window errors.
        """
        # A safe token limit for prompts to avoid hitting the absolute max
        SAFE_CONTEXT_LIMIT = 1000000

        print(f"🔀 Starting recursive synthesis for {len(agent_results)} agents...")

        # Initial list of texts are the raw agent responses
        texts_to_process = [f"Agent {r['agent_id']} (Question: {r['question']}):\n{r['response']}" for r in agent_results]

        # === Recursive Summarization Loop ===
        summarization_level = 1
        while True:
            # Calculate total tokens for the current set of texts
            total_tokens = sum(self.agent.count_tokens(text) for text in texts_to_process)
            print(f"   [Level {summarization_level}] Processing {len(texts_to_process)} text sections with a total of {total_tokens} tokens.")

            # If the total tokens fit into a single prompt, we can break the loop
            if total_tokens <= SAFE_CONTEXT_LIMIT:
                print("   ...content is now small enough for final synthesis.")
                break

            # If not, we run another summarization pass
            print(f"   ...total tokens exceed the safe limit of {SAFE_CONTEXT_LIMIT}. Summarizing in batches.")

            # Group texts into batches that fit within the context window
            groups = self._create_token_aware_groups(texts_to_process, SAFE_CONTEXT_LIMIT)

            new_summaries = []
            for i, group in enumerate(groups):
                print(f"      - Summarizing batch {i+1}/{len(groups)}...")
                # Create a summary for each group
                summary = self._direct_synthesis(query, group, is_sub_synthesis=True)
                if "Error:" in summary:
                    print(f"      ⚠️ Warning: Synthesis for batch {i+1} failed. Skipping.")
                    continue
                new_summaries.append(summary)

            # The new summaries become the texts to process for the next loop iteration
            texts_to_process = new_summaries
            summarization_level += 1

        # === Final Synthesis ===
        print("   ...performing final synthesis.")

        # First, join the processed texts into a single string
        final_reports = "\n\n--- // ---\n\n".join(texts_to_process)

        # Now, create the f-string without backslashes inside the braces
        final_prompt = f"""You have analyzed a research query in several stages.

Original Query: {query}

The findings were recursively summarized. Now, your final task is to synthesize these last reports into a single, cohesive, and comprehensive answer.

Final Reports:
{final_reports}

Provide the final, unified, evidence-based response:"""

        messages = [
            {"role": "system", "content": "You are an expert synthesis AI that combines multiple, pre-summarized reports into a single, final answer."},
            {"role": "user", "content": final_prompt}
        ]

        return self.agent.chat(messages, use_tools=False)


    def _create_token_aware_groups(self, texts: List[str], limit: int) -> List[List[str]]:
        """Groups texts into batches that do not exceed a token limit."""
        groups = []
        current_group = []
        current_tokens = 0

        for text in texts:
            text_tokens = self.agent.count_tokens(text)

            if current_tokens + text_tokens > limit:
                # Current group is full, start a new one
                groups.append(current_group)
                current_group = [text]
                current_tokens = text_tokens
            else:
                # Add to the current group
                current_group.append(text)
                current_tokens += text_tokens

        # Add the last group if it's not empty
        if current_group:
            groups.append(current_group)

        return groups

    def _direct_synthesis(self, query: str, text_group: List[str], is_sub_synthesis: bool = False) -> str:
        """Helper method to run a single synthesis call on a group of texts."""

        # Join the texts in the group into a single block
        responses_text = "\n\n--- // ---\n\n".join(text_group)

        system_prompt = "You are a research assistant. Concisely summarize the key findings and insights from the provided text sections into a single, structured report."

        synthesis_prompt = f"""Based on the original query '{query}', synthesize the following information into a single, coherent report.

Information:
{responses_text}

Synthesized Report:"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": synthesis_prompt}
        ]

        return self.agent.chat(messages, use_tools=False)
    #. Above new
    def orchestrate(self, query: str, progress_callback=None) -> str:
        """Orchestrate multiple agents with full research capabilities"""
        # Generate questions
        if progress_callback:
            progress_callback(f"🎯 Generating {self.num_agents} specialized questions for local and external research...")
        questions = self.generate_questions(query)

        # if progress_callback:
        #     progress_callback(f"📋 Generated {len(questions)} research questions")
        #     for i, q in enumerate(questions[:10]):  # Show first 10 questions
        #         progress_callback(f"  Agent {i+1}: {q}")
        #     if len(questions) > 10:
        #         progress_callback(f"  ... and {len(questions) - 10} more questions")

        if progress_callback:
                progress_callback(f"📋 Generated {len(questions)} research questions. Displaying full list:")
                progress_callback("="*80)
                for i, q in enumerate(questions):
                    # This now loops through ALL questions
                    progress_callback(f"  Agent {i+1}: {q}")
                progress_callback("="*80)

        # Run agents in batches to respect rate limits
        if progress_callback:
            progress_callback(f"\n🔄 Running {self.num_agents} agents with local KB and research tools...")

        agent_results = []
        # max_concurrent = min(self.config['orchestrator'].get('max_concurrent', 2), 2)  # Limit to 2 for rate limits
        max_concurrent = self.config['orchestrator'].get('max_concurrent', 1)
        batch_delay = self.config['orchestrator'].get('batch_delay', 2)

        # Process in batches
        total_batches = (self.num_agents + max_concurrent - 1) // max_concurrent

        for batch_num, batch_start in enumerate(range(0, self.num_agents, max_concurrent), 1):
            batch_end = min(batch_start + max_concurrent, self.num_agents)
            batch_questions = questions[batch_start:batch_end]

            if progress_callback:
                progress_callback(f"\n📦 Processing batch {batch_num}/{total_batches} (Agents {batch_start+1}-{batch_end})...")

            with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
                futures = []
                for i, q in enumerate(batch_questions, start=batch_start):
                    future = executor.submit(self.run_agent_task, i+1, q)
                    futures.append(future)
                    # Small delay between starting agents
                    if i < batch_end - 1:
                        time.sleep(0.5)

                # Collect results as they complete
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        agent_results.append(result)
                        if progress_callback:
                            status_emoji = "✅" if result['status'] != 'error' else "❌"
                            progress_callback(f"{status_emoji} Agent {result['agent_id']} completed in {result['duration']:.2f}s")
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"❌ Agent failed with error: {str(e)}")

            # Wait between batches to respect rate limits
            if batch_end < self.num_agents:
                if progress_callback:
                    progress_callback(f"⏳ Waiting {batch_delay} seconds before next batch (rate limit)...")
                time.sleep(batch_delay)

        # Sort results by agent_id to maintain order
        agent_results.sort(key=lambda x: x['agent_id'])

        # Synthesize all responses
        if progress_callback:
            progress_callback(f"\n🔀 Synthesizing responses from {len(agent_results)} agents...")

        final_response = self.synthesize_responses(query, agent_results)

        if progress_callback:
            progress_callback("\n✨ Analysis complete!")

        return final_response



# ============== UI SETUP ==============
style = """
<style>
.main-container {
    max-width: 1200px;
    margin: 0 auto;
}
.output-container {
    max-height: 600px;
    overflow-y: auto;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    background-color: #f9f9f9;
    font-family: 'Arial', sans-serif;
}
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 20px;
}
.example-box {
    background-color: #e8f4f8;
    border-left: 4px solid #3498db;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
}
.research-box {
    background-color: #f0f8ff;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin-top: 10px;
    border-radius: 5px;
}
.knowledge-box {
    background-color: #fff9e6;
    border-left: 4px solid #ff9800;
    padding: 15px;
    margin-top: 10px;
    border-radius: 5px;
}
.status-box {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    font-weight: bold;
}
.status-success {
    background-color: #d4edda;
    color: #155724;
}
.status-error {
    background-color: #f8d7da;
    color: #721c24;
}
.status-processing {
    background-color: #fff3cd;
    color: #856404;
}
.paper-result {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}
</style>
"""

display(HTML(style))
display(HTML('''
<div class="main-container">
    <div class="header">
        <h1>🚀 Make It Heavy - Gemini 2.5 Pro + Complete Research Suite</h1>
        <p>Multi-Agent AI System with Local Knowledge Base & Academic Research Integration</p>
    </div>
</div>
'''))

# Create UI components
kb_path_input = widgets.Text(
    value=CONFIG['knowledge_base']['base_path'],
    placeholder='/content/drive/MyDrive/BASE KNOWLEDGE',
    description='KB Path:',
    layout=widgets.Layout(width='100%')
)

api_key_input = widgets.Text(
    value=CONFIG['semantic_scholar']['api_key'],
    placeholder='zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn',
    description='S2 API Key:',
    layout=widgets.Layout(width='100%')
)

query_input = widgets.Textarea(
    value='',
    placeholder='Enter your research query... (e.g., "What are the latest advances in multimodal dataset distillation?" or "Compare different approaches to knowledge condensation in neural networks")',
    description='Query:',
    layout=widgets.Layout(width='100%', height='120px')
)

mode_selector = widgets.RadioButtons(
    options=[
        '🤖 Single Agent (Fast)',
        '📚 Single Agent + Local KB',
        '🔬 Single Agent + All Research Tools',
        '🚀 Multi-Agent Orchestrator (Comprehensive)',
        '🎓 Multi-Agent + All Tools (Most Thorough)'
    ],
    description='Mode:',
    value='📚 Single Agent + Local KB',
    layout=widgets.Layout(width='100%')
)

output_area = widgets.Output(layout={'border': '2px solid #e0e0e0', 'height': '500px', 'overflow': 'scroll', 'border-radius': '10px'})

run_button = widgets.Button(
    description='🎯 Run Query',
    button_style='success',
    layout=widgets.Layout(width='200px', height='40px'),
    tooltip='Click to process your query'
)

clear_button = widgets.Button(
    description='🧹 Clear Output',
    button_style='warning',
    layout=widgets.Layout(width='200px', height='40px'),
    tooltip='Clear the output area'
)

test_kb_button = widgets.Button(
    description='📁 Test Local KB',
    button_style='info',
    layout=widgets.Layout(width='200px', height='40px'),
    tooltip='Test local knowledge base connection'
)

status_label = widgets.HTML(value='<div class="status-box status-success">✅ Ready to process queries...</div>')

progress_bar = widgets.IntProgress(
    value=0,
    min=0,
    max=100,
    description='Progress:',
    bar_style='info',
    layout=widgets.Layout(width='100%', visibility='hidden')
)

def test_kb_connection(b):
    """Test local knowledge base connection"""
    with output_area:
        clear_output()
        print("🔍 Testing Local Knowledge Base...")

        # Update config with KB path
        CONFIG['knowledge_base']['base_path'] = kb_path_input.value

        try:
            kb_tool = LocalKnowledgeBaseTool(kb_path_input.value)
            num_docs = len(kb_tool.documents)

            print(f"✅ Knowledge base connected successfully!")
            print(f"📚 Found {num_docs} documents")

            if num_docs > 0:
                print("\n📄 Sample documents:")
                for i, (filename, doc_info) in enumerate(list(kb_tool.documents.items())[:5]):
                    print(f"  {i+1}. {doc_info['title']} ({filename})")
                    print(f"     Length: {doc_info['length']} characters")

                # Test search
                test_result = kb_tool.execute("dataset distillation", limit=2)
                if test_result['status'] == 'success' and test_result['num_results'] > 0:
                    print(f"\n🔍 Test search found {test_result['num_results']} relevant documents")
            else:
                print(f"\n⚠️ No documents found in {kb_path_input.value}")
                print("Please ensure your markdown files are in the specified folder.")

        except Exception as e:
            print(f"❌ Error accessing knowledge base: {e}")

def clear_output_handler(b):
    with output_area:
        clear_output()
    status_label.value = '<div class="status-box status-success">✅ Output cleared. Ready for new query...</div>'
    progress_bar.layout.visibility = 'hidden'
    progress_bar.value = 0

def run_query(b):
    query = query_input.value.strip()
    if not query:
        status_label.value = '<div class="status-box status-error">❌ Please enter a query!</div>'
        return

    # Update configs
    CONFIG['knowledge_base']['base_path'] = kb_path_input.value
    CONFIG['semantic_scholar']['api_key'] = api_key_input.value

    status_label.value = '<div class="status-box status-processing">🔄 Processing your query...</div>'
    run_button.disabled = True

    with output_area:
        clear_output()
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        print("="*80)
        print(f"🕐 Timestamp: {timestamp}")
        print(f"📝 Query: {query}")
        print("="*80)

        try:
            if '🤖 Single Agent (Fast)' in mode_selector.value:
                print("\n🤖 SINGLE AGENT MODE - Fast Response")
                print("-"*50)

                agent = GeminiAgentWithTools(CONFIG, silent=False)
                messages = [
                    {"role": "system", "content": "You are a helpful AI assistant."},
                    {"role": "user", "content": query}
                ]
                result = agent.chat(messages, use_tools=False)

                print("\n" + "="*80)
                print("📋 RESPONSE:")
                print("="*80)
                print(result)

                status_label.value = '<div class="status-box status-success">✅ Query completed!</div>'

            elif '📚 Single Agent + Local KB' in mode_selector.value:
                print("\n📚 SINGLE AGENT WITH LOCAL KNOWLEDGE BASE MODE")
                print("-"*50)

                # Create agent with only local KB tools
                agent = GeminiAgentWithTools(CONFIG, silent=False)
                # Temporarily remove external tools
                external_tools = ['search_research_papers', 'get_paper_details']
                saved_tools = {}
                for tool_name in external_tools:
                    if tool_name in agent.tools:
                        saved_tools[tool_name] = agent.tools.pop(tool_name)

                result = agent.run_task_with_tools(query)

                # Restore tools
                agent.tools.update(saved_tools)

                print("\n" + "="*80)
                print("📋 LOCAL KNOWLEDGE-BASED RESPONSE:")
                print("="*80)
                print(result['result'])

                status_label.value = '<div class="status-box status-success">✅ Local KB query completed!</div>'

            elif '🔬 Single Agent + All Research Tools' in mode_selector.value:
                print("\n🔬 SINGLE AGENT WITH ALL RESEARCH TOOLS MODE")
                print("-"*100)

                agent = GeminiAgentWithTools(CONFIG, silent=False)
                result = agent.run_task_with_tools(query)

                print("\n" + "="*80)
                print("📋 COMPREHENSIVE RESEARCH RESPONSE:")
                print("="*80)
                print(result['result'])

                status_label.value = '<div class="status-box status-success">✅ Research query completed!</div>'

            elif '🚀 Multi-Agent Orchestrator (Comprehensive)' in mode_selector.value:
                print("\n🚀 MULTI-AGENT ORCHESTRATOR MODE")
                print("-"*100)

                progress_bar.layout.visibility = 'visible'
                progress_bar.value = 10

                orchestrator = GeminiOrchestratorSimple(CONFIG)

                def progress_callback(msg):
                    print(msg)
                    progress_bar.value = min(progress_bar.value + 10, 90)

                final_response = orchestrator.orchestrate(query, progress_callback)

                progress_bar.value = 100

                print("\n" + "="*80)
                print("📋 SYNTHESIZED COMPREHENSIVE RESPONSE:")
                print("="*80)
                print(final_response)

                status_label.value = '<div class="status-box status-success">✅ Multi-agent analysis completed!</div>'

            else:  # Multi-Agent + All Tools
                print("\n🎓 MULTI-AGENT WITH ALL RESEARCH TOOLS MODE - Ultimate Analysis")
                print("-"*100)

                progress_bar.layout.visibility = 'visible'
                progress_bar.value = 10

                orchestrator = GeminiOrchestratorWithTools(CONFIG)

                def progress_callback(msg):
                    print(msg)
                    progress_bar.value = min(progress_bar.value + 15, 90)

                final_response = orchestrator.orchestrate(query, progress_callback)

                progress_bar.value = 100

                print("\n" + "="*80)
                print("📋 ULTIMATE SYNTHESIZED RESEARCH RESPONSE:")
                print("="*80)
                print(final_response)

                status_label.value = '<div class="status-box status-success">✅ Complete research analysis finished!</div>'

        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()
            status_label.value = f'<div class="status-box status-error">❌ Error: {str(e)}</div>'
        finally:
            run_button.disabled = False
            if progress_bar.layout.visibility == 'visible':
                time.sleep(1)
                progress_bar.layout.visibility = 'hidden'
                progress_bar.value = 0

# Bind handlers
run_button.on_click(run_query)
clear_button.on_click(clear_output_handler)
test_kb_button.on_click(test_kb_connection)

# Layout
button_box = widgets.HBox([run_button, clear_button, test_kb_button], layout=widgets.Layout(justify_content='center', margin='10px'))
control_box = widgets.VBox([
    kb_path_input,
    api_key_input,
    mode_selector,
    query_input,
    button_box,
    progress_bar,
    status_label
], layout=widgets.Layout(padding='20px', border='2px solid #e0e0e0', border_radius='10px'))

# Display UI
display(control_box)
display(output_area)

# Example queries and info
info_html = """
<div class="example-box">
<h3>💡 Example Research Queries:</h3>
<ul style="line-height: 1.8;">
<li><strong>Dataset Distillation:</strong> "What are the latest techniques for multimodal dataset distillation?"</li>
<li><strong>Knowledge Condensation:</strong> "Compare different approaches to knowledge condensation in neural networks"</li>
<li><strong>Research Synthesis:</strong> "How do dataset distillation methods handle cross-modal information?"</li>
<li><strong>Innovation Analysis:</strong> "What are novel applications of dataset condensation in computer vision?"</li>
<li><strong>Literature Review:</strong> "Summarize recent advances in gradient-based dataset distillation"</li>
</ul>
</div>

<div class="knowledge-box">
<h3>📚 Local Knowledge Base Features:</h3>
<ul>
<li>🔍 <strong>Smart Search:</strong> Search through your personal collection of research papers</li>
<li>📄 <strong>Full Document Access:</strong> Read complete papers or specific sections</li>
<li>🎯 <strong>Relevance Ranking:</strong> Find the most relevant documents for your query</li>
<li>📊 <strong>Multi-Document Analysis:</strong> Compare findings across multiple papers</li>
<li>🔗 <strong>Integrated Citations:</strong> Properly cite papers from your collection</li>
</ul>
<p><em>📁 Place your .md files in: {kb_path}</em></p>
</div>

<div class="research-box">
<h3>🔬 Complete Research Suite:</h3>
<ul>
<li>📚 <strong>Local + External:</strong> Combines your personal papers with Semantic Scholar database</li>
<li>🤝 <strong>Cross-Reference:</strong> Validates findings across multiple sources</li>
<li>🎓 <strong>Multi-Agent Research:</strong> Different agents explore different aspects simultaneously</li>
<li>📈 <strong>Evidence Synthesis:</strong> Comprehensive analysis backed by multiple sources</li>
<li>🔄 <strong>Iterative Refinement:</strong> Agents can dig deeper based on initial findings</li>
</ul>
<p><em>💡 Tip: Use "Multi-Agent + All Tools" for the most comprehensive research analysis!</em></p>
</div>
""".format(kb_path=CONFIG['knowledge_base']['base_path'])

display(HTML(info_html))

# Test connections
print("\n🔍 Testing connections...")
try:
    # Test Gemini
    test_model = genai.GenerativeModel('gemini-2.5-pro')
    test_response = test_model.generate_content("Say 'Gemini ready!' in 5 words or less.")
    print(f"✅ Gemini 2.5 Pro: {test_response.text.strip()}")

    # Test Local KB
    kb_tool = LocalKnowledgeBaseTool()
    print(f"✅ Local Knowledge Base: {len(kb_tool.documents)} documents loaded")

    # Test Semantic Scholar - Fixed version
    time.sleep(1)  # Respect rate limit
    s2_key = CONFIG['semantic_scholar']['api_key']

    # Check if key exists in UI widget
    try:
        if api_key_input.value and api_key_input.value != "YOUR_SEMANTIC_SCHOLAR_API_KEY_HERE":
            s2_key = api_key_input.value
    except:
        pass

    if s2_key and s2_key != "YOUR_SEMANTIC_SCHOLAR_API_KEY_HERE":
        tool = SemanticScholarTool(s2_key)
        result = tool.execute("test", limit=1)
        if result['status'] == 'success':
            print(f"✅ Semantic Scholar API: Connected successfully!")
        else:
            print(f"⚠️ Semantic Scholar API: {result.get('error', 'Check failed')}")
    else:
        print("ℹ️ Semantic Scholar API: No API key provided (optional)")

except Exception as e:
    print(f"❌ Connection test failed: {e}")

print("\n✨ System ready! Choose a mode and enter your research query.")

# ============== LARGE-SCALE ORCHESTRATION INTERFACE ==============

print("\n" + "="*80)
print("🚀 LARGE-SCALE RESEARCH ORCHESTRATION SYSTEM")
print("="*80)

# Progress monitoring for large-scale orchestration
class ProgressMonitor:
    def __init__(self):
        self.start_time = None
        self.progress_widget = None
        self.status_widget = None

    def setup_widgets(self, total_agents: int):
        """Setup progress monitoring widgets"""
        self.start_time = time.time()

        # Progress bar
        self.progress_widget = widgets.IntProgress(
            value=0,
            min=0,
            max=total_agents,
            description='Progress:',
            bar_style='info',
            style={'bar_color': '#1f77b4'},
            orientation='horizontal'
        )

        # Status text
        self.status_widget = widgets.HTML(
            value="<b>Starting orchestration...</b>"
        )

        # Display widgets
        display(widgets.VBox([self.progress_widget, self.status_widget]))

    def update_progress(self, completed: int, total: int, last_result: dict = None):
        """Update progress display"""
        if self.progress_widget:
            self.progress_widget.value = completed

        if self.status_widget:
            elapsed = time.time() - self.start_time
            rate = completed / elapsed if elapsed > 0 else 0
            eta = (total - completed) / rate if rate > 0 else 0

            status_html = f"""
            <b>Progress: {completed}/{total} agents completed</b><br>
            <b>Elapsed:</b> {elapsed:.1f}s | <b>Rate:</b> {rate:.2f} agents/s | <b>ETA:</b> {eta:.1f}s<br>
            """

            if last_result:
                status_color = "green" if last_result['status'] == 'completed' else "red"
                status_html += f"""<b>Last Agent:</b> <span style="color:{status_color}">Agent {last_result['agent_id']} - {last_result['status']}</span>"""

            self.status_widget.value = status_html

# Main execution function for large-scale orchestration
def run_large_scale_research_orchestration(query: str, num_agents: int = 100, max_concurrent: int = 2,
                                         batch_delay: int = 5, resume_session: str = None):
    """
    Main function to run large-scale research orchestration

    Args:
        query: Research query to investigate
        num_agents: Total number of agents to deploy (default: 100)
        max_concurrent: Maximum concurrent agents (default: 2)
        batch_delay: Delay between batches in seconds (default: 5)
        resume_session: Session ID to resume from checkpoint
    """

    # Update configuration
    config = CONFIG.copy()
    config['orchestrator']['parallel_agents'] = num_agents
    config['orchestrator']['max_concurrent'] = max_concurrent
    config['orchestrator']['batch_delay'] = batch_delay

    # Create orchestrator
    orchestrator = GeminiOrchestratorWithTools(config)

    # Set session ID
    if resume_session:
        orchestrator.set_session_id(resume_session)
        print(f"🔄 Resuming session: {resume_session}")
    else:
        session_id = f"research_{int(time.time())}"
        orchestrator.set_session_id(session_id)
        print(f"🆕 Starting new session: {session_id}")

    # Setup progress monitoring
    monitor = ProgressMonitor()
    monitor.setup_widgets(num_agents)

    # Define progress callback
    def progress_callback(completed, total, last_result):
        monitor.update_progress(completed, total, last_result)

    try:
        # Run orchestration
        results = orchestrator.run_sequential_batch_orchestration(query, progress_callback)

        # Display final results
        print("\n" + "="*80)
        print("🎉 ORCHESTRATION COMPLETED!")
        print("="*80)
        print(f"Query: {query}")
        print(f"Total Agents: {results['total_agents']}")
        print(f"Session ID: {results['session_id']}")
        print(f"Statistics: {results['statistics']}")
        print("\n📋 FINAL SYNTHESIS:")
        print("-" * 40)
        print(results['final_synthesis'])

        return results

    except KeyboardInterrupt:
        print("\n⏹️ Orchestration interrupted. Progress saved to checkpoint.")
        print(f"Resume with: run_large_scale_research_orchestration('{query}', resume_session='{orchestrator.session_id}')")
        return None
    except Exception as e:
        print(f"\n❌ Orchestration failed: {str(e)}")
        return None

# Utility functions
def list_available_checkpoints():
    """List all available checkpoint sessions"""
    checkpoint_manager = CheckpointManager()
    checkpoints = checkpoint_manager.list_checkpoints()

    if not checkpoints:
        print("No checkpoints found.")
        return []

    print("Available checkpoint sessions:")
    for i, session_id in enumerate(checkpoints, 1):
        try:
            data = checkpoint_manager.load_checkpoint(session_id)
            query = data.get('query', 'Unknown query')
            completed = len(data.get('completed_agents', []))
            total = data.get('total_agents', 0)
            print(f"{i}. {session_id}")
            print(f"   Query: {query}")
            print(f"   Progress: {completed}/{total} agents")
            print()
        except:
            print(f"{i}. {session_id} (corrupted)")

    return checkpoints

def load_checkpoint_results(session_id: str):
    """Load and display results from a checkpoint"""
    checkpoint_manager = CheckpointManager()

    # Try to load final results first
    final_data = checkpoint_manager.load_checkpoint(f"{session_id}_final")
    if final_data:
        print(f"📊 Final results for session: {session_id}")
        print(f"Query: {final_data.get('query', 'Unknown')}")
        print(f"Completion time: {final_data.get('completion_time', 'Unknown')}")
        print(f"Total agents: {final_data.get('total_agents', 0)}")
        print("\n📋 Final synthesis:")
        print(final_data.get('final_synthesis', 'No synthesis available'))
        return final_data

    # Load regular checkpoint
    data = checkpoint_manager.load_checkpoint(session_id)
    if data:
        print(f"📊 Checkpoint data for session: {session_id}")
        print(f"Query: {data.get('query', 'Unknown')}")
        completed = len(data.get('completed_agents', []))
        total = data.get('total_agents', 0)
        print(f"Progress: {completed}/{total} agents")

        if completed > 0:
            print(f"Batch completed: {data.get('batch_completed', 0)}/{data.get('total_batches', 0)}")

        return data
    else:
        print(f"❌ No checkpoint found for session: {session_id}")
        return None

def test_small_scale_orchestration(query: str = "dataset distillation for computer vision", num_agents: int = 5):
    """Test the system with a small number of agents"""
    print(f"🧪 Testing with {num_agents} agents...")
    return run_large_scale_research_orchestration(
        query=query,
        num_agents=num_agents,
        max_concurrent=2,
        batch_delay=2
    )

# Example usage display
def show_large_scale_examples():
    """Show example usage of the large-scale system"""
    examples_html = """
    <div class="example-box">
    <h3>🚀 Large-Scale Orchestration Examples</h3>

    <h4>📝 Test with Small Scale (5 agents):</h4>
    <code>test_small_scale_orchestration("multimodal dataset distillation")</code>

    <h4>🎯 Full 100-Agent Orchestration:</h4>
    <code>
    run_large_scale_research_orchestration(<br>
    &nbsp;&nbsp;&nbsp;&nbsp;"multimodal dataset distillation for tri-modal learning",<br>
    &nbsp;&nbsp;&nbsp;&nbsp;num_agents=100,<br>
    &nbsp;&nbsp;&nbsp;&nbsp;max_concurrent=2,<br>
    &nbsp;&nbsp;&nbsp;&nbsp;batch_delay=5<br>
    )
    </code>

    <h4>🔄 Resume from Checkpoint:</h4>
    <code>
    list_available_checkpoints()<br>
    run_large_scale_research_orchestration(<br>
    &nbsp;&nbsp;&nbsp;&nbsp;"your query",<br>
    &nbsp;&nbsp;&nbsp;&nbsp;resume_session="session_1234567890"<br>
    )
    </code>

    <h4>📊 Load Results:</h4>
    <code>load_checkpoint_results("session_1234567890")</code>

    <h4>⚙️ Configuration Options:</h4>
    <ul>
    <li><strong>num_agents:</strong> Total agents (1-100+)</li>
    <li><strong>max_concurrent:</strong> Concurrent agents (1-2 recommended for API limits)</li>
    <li><strong>batch_delay:</strong> Seconds between batches (5+ recommended)</li>
    </ul>

    <h4>💾 Checkpoint Features:</h4>
    <ul>
    <li>Automatic progress saving after each batch</li>
    <li>Resume interrupted sessions</li>
    <li>Google Drive integration for persistence</li>
    <li>Final results preservation</li>
    </ul>
    </div>
    """
    display(HTML(examples_html))

# Display the examples
show_large_scale_examples()

print("\n🎯 Ready for large-scale research orchestration!")
print("💡 Start with: test_small_scale_orchestration() to verify the system")
print("🚀 Then scale up to: run_large_scale_research_orchestration() for full analysis")

