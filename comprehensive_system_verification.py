#!/usr/bin/env python3
"""
Comprehensive verification of the 4-phase multimodal research system
"""

import os
import json
import time
from multimodal_research_orchestrator import MultimodalResearchOrchestrator
from hybrid_agent import HybridAgent

def test_knowledge_base_access():
    """Test access to knowledge base and MMIS paper"""
    print("📚 Testing Knowledge Base Access")
    print("=" * 50)
    
    # Check if knowledge base exists
    kb_path = "D:/Downloads/make-it-heavy-main/research_knowledge_base"
    if not os.path.exists(kb_path):
        print(f"❌ Knowledge base not found at: {kb_path}")
        return False
    
    # Check for MMIS paper
    mmis_file = None
    for file in os.listdir(kb_path):
        if "MMIS" in file and file.endswith('.md'):
            mmis_file = os.path.join(kb_path, file)
            break
    
    if mmis_file:
        print(f"✅ MMIS paper found: {os.path.basename(mmis_file)}")
    else:
        print("⚠️ MMIS paper not found in knowledge base")
    
    # Test agent access to knowledge base
    try:
        agent = HybridAgent(silent=True)
        
        # Test reading a file from knowledge base
        test_query = f"Read and summarize the MMIS dataset paper from the knowledge base using the read_file tool"
        response = agent.run(test_query)
        
        if response and len(response) > 100:
            print("✅ Agent can access knowledge base successfully")
            print(f"📝 Response preview: {response[:150]}...")
            return True
        else:
            print("❌ Agent cannot access knowledge base properly")
            return False
            
    except Exception as e:
        print(f"❌ Knowledge base access test failed: {e}")
        return False

def test_phase_question_generation():
    """Test that each phase generates appropriate questions"""
    print("\n🔍 Testing Phase Question Generation")
    print("=" * 50)
    
    orchestrator = MultimodalResearchOrchestrator(silent=True)
    
    phase_tests = []
    for phase in range(1, 5):
        print(f"\nPhase {phase}: {orchestrator.phases[phase]}")
        
        # Generate 3 test questions
        questions = orchestrator.generate_phase_specific_questions(phase, 3)
        
        # Verify questions contain phase-specific content
        phase_keywords = {
            1: ["limitation", "computational", "complexity", "generalization", "modality collapse"],
            2: ["informativeness", "soft label", "DD-Ranking", "diversity", "metrics"],
            3: ["MFDD", "algorithm", "prototype", "loss function", "multimodal"],
            4: ["evaluation", "benchmark", "verification", "open-source", "protocol"]
        }
        
        keywords_found = 0
        for question in questions:
            for keyword in phase_keywords[phase]:
                if keyword.lower() in question.lower():
                    keywords_found += 1
                    break
        
        success = keywords_found >= 2  # At least 2 questions should contain phase keywords
        phase_tests.append(success)
        
        print(f"   ✅ Generated {len(questions)} questions")
        print(f"   ✅ Phase-specific content: {'PASS' if success else 'FAIL'}")
        
        # Show sample question focus
        if questions:
            focus = questions[0].split('Specific Focus Area: ')[1].split('Agent ID:')[0].strip()
            print(f"   📋 Sample focus: {focus}")
    
    overall_success = all(phase_tests)
    print(f"\n📊 Question Generation: {'✅ PASS' if overall_success else '❌ FAIL'}")
    return overall_success

def test_single_agent_execution():
    """Test single agent execution with research tools"""
    print("\n🤖 Testing Single Agent Execution")
    print("=" * 50)
    
    try:
        orchestrator = MultimodalResearchOrchestrator(silent=True)
        
        # Create a focused research question
        test_question = """
        You are a well-known ambitious doctor in AI specialized in dataset distillation with very strong background in Math.
        
        Focus on Phase 1: Analyze computational complexity limitations in multimodal dataset distillation.
        
        Your task:
        1. Search for recent papers on dataset distillation computational challenges
        2. Analyze the bi-level optimization bottlenecks
        3. Propose mathematical formulations for complexity reduction
        4. Use the knowledge base to reference existing research
        
        Provide a comprehensive analysis with mathematical insights.
        """
        
        print("🔄 Running test agent...")
        start_time = time.time()
        
        result = orchestrator.run_single_agent(999, 1, test_question)
        
        execution_time = time.time() - start_time
        
        if result["status"] == "success":
            print(f"✅ Agent execution successful")
            print(f"⏱️ Execution time: {execution_time:.1f}s")
            print(f"📝 Response length: {len(result['response'])} characters")
            print(f"📝 Response preview: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Agent execution failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Agent execution test failed: {e}")
        return False

def test_synthesis_process():
    """Test the synthesis process with mock results"""
    print("\n🔄 Testing Synthesis Process")
    print("=" * 50)
    
    try:
        orchestrator = MultimodalResearchOrchestrator(silent=True)
        
        # Create mock phase results
        mock_results = {
            "phase": 1,
            "phase_name": "Comprehensive Analysis of Common Limitations",
            "total_agents": 2,
            "successful": 2,
            "failed": 0,
            "results": [
                {
                    "agent_id": 0,
                    "phase": 1,
                    "status": "success",
                    "response": "Computational complexity in multimodal dataset distillation stems from bi-level optimization frameworks that require extensive gradient unrolling. The memory overhead scales quadratically with dataset size, particularly problematic for high-resolution multimodal data.",
                    "execution_time": 120
                },
                {
                    "agent_id": 1,
                    "phase": 1,
                    "status": "success", 
                    "response": "Cross-architecture generalization failures occur due to architecture-specific feature representations learned during distillation. The synthetic data becomes overfitted to the teacher model's inductive biases, limiting transferability to unseen architectures.",
                    "execution_time": 115
                }
            ]
        }
        
        print("🔄 Running synthesis...")
        synthesis = orchestrator.synthesize_phase_results(mock_results)
        
        if synthesis and len(synthesis) > 200:
            print("✅ Synthesis successful")
            print(f"📝 Synthesis length: {len(synthesis)} characters")
            print(f"📝 Synthesis preview: {synthesis[:200]}...")
            return True
        else:
            print("❌ Synthesis failed or too short")
            return False
            
    except Exception as e:
        print(f"❌ Synthesis test failed: {e}")
        return False

def test_checkpoint_system():
    """Test checkpoint saving and loading"""
    print("\n💾 Testing Checkpoint System")
    print("=" * 50)
    
    try:
        # Test checkpoint directory
        checkpoint_dir = "./research_checkpoints"
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
            print("✅ Checkpoint directory created")
        else:
            print("✅ Checkpoint directory exists")
        
        # Test saving a checkpoint
        test_data = {
            "session_id": "test_verification",
            "phase": 1,
            "completed_agents": [0, 1],
            "timestamp": time.time()
        }
        
        checkpoint_file = os.path.join(checkpoint_dir, "test_verification_checkpoint.json")
        with open(checkpoint_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        print("✅ Checkpoint save successful")
        
        # Test loading checkpoint
        with open(checkpoint_file, 'r') as f:
            loaded_data = json.load(f)
        
        if loaded_data["session_id"] == test_data["session_id"]:
            print("✅ Checkpoint load successful")
            
            # Clean up
            os.remove(checkpoint_file)
            print("✅ Checkpoint cleanup complete")
            return True
        else:
            print("❌ Checkpoint data mismatch")
            return False
            
    except Exception as e:
        print(f"❌ Checkpoint test failed: {e}")
        return False

def run_comprehensive_verification():
    """Run all verification tests"""
    print("🔬 COMPREHENSIVE 4-PHASE SYSTEM VERIFICATION")
    print("=" * 70)
    
    tests = [
        ("Knowledge Base Access", test_knowledge_base_access),
        ("Phase Question Generation", test_phase_question_generation),
        ("Single Agent Execution", test_single_agent_execution),
        ("Synthesis Process", test_synthesis_process),
        ("Checkpoint System", test_checkpoint_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 VERIFICATION SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL VERIFICATION TESTS PASSED!")
        print("🚀 System ready for full 4-phase research execution")
        return True
    else:
        print("⚠️ Some tests failed - system needs attention before full deployment")
        return False

if __name__ == "__main__":
    success = run_comprehensive_verification()
    
    if success:
        print(f"\n🎯 Next Steps:")
        print(f"1. Run full 4-phase research: python run_multimodal_research.py")
        print(f"2. Expected duration: 3-4 hours with 100 agents")
        print(f"3. Output: Complete MFDD research proposal")
    else:
        print(f"\n🔧 Fix the failed tests before proceeding with full research")
