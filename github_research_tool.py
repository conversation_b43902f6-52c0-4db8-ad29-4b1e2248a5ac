#!/usr/bin/env python3
"""
GitHub Research Tool

This tool allows agents to search GitHub repositories for research paper implementations,
analyze code structure, read documentation, and extract implementation details.

Features:
- Search repositories by research topic/paper title
- Find repositories linked to specific papers
- Analyze repository structure and code
- Extract README and documentation
- Identify key implementation files
- Check repository metrics (stars, forks, activity)
"""

import requests
import base64
import time
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
import re
import os

class BaseTool(ABC):
    """Base class for all tools"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> dict:
        pass
    
    @abstractmethod
    def execute(self, **kwargs) -> dict:
        pass

class GitHubResearchTool(BaseTool):
    """Tool for searching and analyzing GitHub repositories related to research papers"""
    
    def __init__(self, github_token: str = None):
        self.github_token = github_token
        self.base_url = "https://api.github.com"
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "Research-Agent/1.0"
        }
        
        if github_token:
            self.headers["Authorization"] = f"token {github_token}"
            print("✅ GitHub API authenticated with token")
        else:
            print("⚠️ GitHub API running without authentication (rate limited)")
    
    @property
    def name(self) -> str:
        return "search_github_research"
    
    @property
    def description(self) -> str:
        return "Search GitHub repositories for research paper implementations and analyze code"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query (paper title, research topic, or specific technique)"
                },
                "search_type": {
                    "type": "string",
                    "enum": ["repositories", "code", "papers"],
                    "description": "Type of search to perform",
                    "default": "repositories"
                },
                "language": {
                    "type": "string",
                    "description": "Programming language filter (e.g., 'python', 'pytorch')",
                    "default": ""
                },
                "sort": {
                    "type": "string",
                    "enum": ["stars", "forks", "updated", "relevance"],
                    "description": "Sort results by",
                    "default": "stars"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results",
                    "default": 10,
                    "maximum": 50
                },
                "analyze_repo": {
                    "type": "boolean",
                    "description": "Whether to analyze repository structure and code",
                    "default": True
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, search_type: str = "repositories", language: str = "", 
                sort: str = "stars", limit: int = 10, analyze_repo: bool = True, **kwargs) -> dict:
        try:
            if search_type == "repositories":
                return self._search_repositories(query, language, sort, limit, analyze_repo)
            elif search_type == "code":
                return self._search_code(query, language, limit)
            elif search_type == "papers":
                return self._search_paper_repos(query, limit, analyze_repo)
            else:
                return {"status": "error", "error": f"Unknown search type: {search_type}"}
                
        except Exception as e:
            return {
                "status": "error",
                "error": f"GitHub search failed: {str(e)}",
                "query": query
            }
    
    def _search_repositories(self, query: str, language: str, sort: str, limit: int, analyze: bool) -> dict:
        """Search GitHub repositories"""
        
        # Build search query
        search_query = f"{query}"
        if language:
            search_query += f" language:{language}"
        
        # Add research-specific keywords to improve relevance
        research_keywords = ["paper", "implementation", "pytorch", "tensorflow", "research"]
        search_query += f" {' OR '.join(research_keywords)}"
        
        url = f"{self.base_url}/search/repositories"
        params = {
            "q": search_query,
            "sort": sort,
            "order": "desc",
            "per_page": min(limit, 30)
        }
        
        print(f"🔍 Searching GitHub repositories for: '{query}'")
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        
        if response.status_code == 403:
            return {
                "status": "error",
                "error": "GitHub API rate limit exceeded. Please provide a GitHub token.",
                "query": query
            }
        
        response.raise_for_status()
        data = response.json()
        
        repositories = []
        for repo in data.get("items", [])[:limit]:
            repo_info = {
                "name": repo["name"],
                "full_name": repo["full_name"],
                "description": repo.get("description", "No description"),
                "url": repo["html_url"],
                "clone_url": repo["clone_url"],
                "stars": repo["stargazers_count"],
                "forks": repo["forks_count"],
                "language": repo.get("language", "Unknown"),
                "updated_at": repo["updated_at"],
                "topics": repo.get("topics", []),
                "size": repo["size"]
            }
            
            # Analyze repository if requested
            if analyze:
                analysis = self._analyze_repository(repo["full_name"])
                repo_info["analysis"] = analysis
            
            repositories.append(repo_info)
        
        return {
            "status": "success",
            "query": query,
            "total_found": data.get("total_count", 0),
            "repositories": repositories,
            "search_type": "repositories"
        }
    
    def _search_code(self, query: str, language: str, limit: int) -> dict:
        """Search for specific code implementations"""
        
        search_query = f"{query}"
        if language:
            search_query += f" language:{language}"
        
        url = f"{self.base_url}/search/code"
        params = {
            "q": search_query,
            "per_page": min(limit, 30)
        }
        
        print(f"🔍 Searching GitHub code for: '{query}'")
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        code_results = []
        for item in data.get("items", [])[:limit]:
            code_info = {
                "name": item["name"],
                "path": item["path"],
                "repository": item["repository"]["full_name"],
                "url": item["html_url"],
                "language": item.get("language", "Unknown"),
                "size": item["size"]
            }
            
            # Get file content if it's small enough
            if item["size"] < 10000:  # Less than 10KB
                content = self._get_file_content(item["repository"]["full_name"], item["path"])
                if content:
                    code_info["content_preview"] = content[:500] + "..." if len(content) > 500 else content
            
            code_results.append(code_info)
        
        return {
            "status": "success",
            "query": query,
            "total_found": data.get("total_count", 0),
            "code_files": code_results,
            "search_type": "code"
        }
    
    def _search_paper_repos(self, query: str, limit: int, analyze: bool) -> dict:
        """Search for repositories specifically linked to research papers"""
        
        # Enhanced search for paper implementations
        paper_query = f"{query} (paper OR arxiv OR implementation OR reproduce)"
        
        url = f"{self.base_url}/search/repositories"
        params = {
            "q": paper_query,
            "sort": "stars",
            "order": "desc",
            "per_page": min(limit, 30)
        }
        
        print(f"🔍 Searching for paper implementations: '{query}'")
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        paper_repos = []
        for repo in data.get("items", [])[:limit]:
            # Check if repository likely contains paper implementation
            is_paper_repo = self._is_paper_repository(repo)
            
            if is_paper_repo["is_paper_repo"]:
                repo_info = {
                    "name": repo["name"],
                    "full_name": repo["full_name"],
                    "description": repo.get("description", ""),
                    "url": repo["html_url"],
                    "stars": repo["stargazers_count"],
                    "forks": repo["forks_count"],
                    "language": repo.get("language", "Unknown"),
                    "paper_indicators": is_paper_repo["indicators"],
                    "confidence": is_paper_repo["confidence"]
                }
                
                if analyze:
                    analysis = self._analyze_repository(repo["full_name"])
                    repo_info["analysis"] = analysis
                
                paper_repos.append(repo_info)
        
        return {
            "status": "success",
            "query": query,
            "total_found": len(paper_repos),
            "paper_repositories": paper_repos,
            "search_type": "papers"
        }
    
    def _analyze_repository(self, full_name: str) -> dict:
        """Analyze repository structure and extract key information"""
        try:
            # Get repository details
            repo_url = f"{self.base_url}/repos/{full_name}"
            repo_response = requests.get(repo_url, headers=self.headers, timeout=30)
            repo_data = repo_response.json()
            
            analysis = {
                "readme": None,
                "key_files": [],
                "structure": {},
                "paper_links": [],
                "implementation_details": {}
            }
            
            # Get README
            readme_content = self._get_readme(full_name)
            if readme_content:
                analysis["readme"] = readme_content[:1000] + "..." if len(readme_content) > 1000 else readme_content
                analysis["paper_links"] = self._extract_paper_links(readme_content)
            
            # Get repository structure
            contents_url = f"{self.base_url}/repos/{full_name}/contents"
            contents_response = requests.get(contents_url, headers=self.headers, timeout=30)
            
            if contents_response.status_code == 200:
                contents = contents_response.json()
                analysis["structure"] = self._analyze_structure(contents)
                analysis["key_files"] = self._identify_key_files(contents)
            
            return analysis
            
        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}
    
    def _get_readme(self, full_name: str) -> Optional[str]:
        """Get README content from repository"""
        readme_files = ["README.md", "README.rst", "README.txt", "README"]
        
        for readme_file in readme_files:
            try:
                content = self._get_file_content(full_name, readme_file)
                if content:
                    return content
            except:
                continue
        
        return None
    
    def _get_file_content(self, full_name: str, file_path: str) -> Optional[str]:
        """Get content of a specific file"""
        try:
            url = f"{self.base_url}/repos/{full_name}/contents/{file_path}"
            response = requests.get(url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("encoding") == "base64":
                    content = base64.b64decode(data["content"]).decode("utf-8")
                    return content
            
            return None
            
        except Exception:
            return None
    
    def _is_paper_repository(self, repo: dict) -> dict:
        """Determine if repository likely contains paper implementation"""
        indicators = []
        confidence = 0.0
        
        description = (repo.get("description") or "").lower()
        name = repo["name"].lower()
        topics = repo.get("topics", [])
        
        # Check for paper-related keywords
        paper_keywords = ["paper", "arxiv", "implementation", "reproduce", "replication", 
                         "official", "pytorch", "tensorflow", "research"]
        
        for keyword in paper_keywords:
            if keyword in description or keyword in name:
                indicators.append(f"Contains '{keyword}' in name/description")
                confidence += 0.15
        
        # Check topics
        research_topics = ["machine-learning", "deep-learning", "computer-vision", 
                          "natural-language-processing", "research", "paper"]
        
        for topic in topics:
            if topic in research_topics:
                indicators.append(f"Has research topic: {topic}")
                confidence += 0.1
        
        # Check for high star count (popular implementations)
        if repo["stargazers_count"] > 100:
            indicators.append(f"High star count: {repo['stargazers_count']}")
            confidence += 0.1
        
        return {
            "is_paper_repo": confidence > 0.3,
            "confidence": min(confidence, 1.0),
            "indicators": indicators
        }
    
    def _extract_paper_links(self, readme_content: str) -> List[str]:
        """Extract paper links from README content"""
        paper_links = []
        
        # Look for arXiv links
        arxiv_pattern = r'https?://arxiv\.org/(?:abs/|pdf/)?(\d{4}\.\d{4,5})'
        arxiv_matches = re.findall(arxiv_pattern, readme_content)
        paper_links.extend([f"https://arxiv.org/abs/{match}" for match in arxiv_matches])
        
        # Look for other paper links
        paper_patterns = [
            r'https?://[^\s]+\.pdf',
            r'https?://papers\.nips\.cc/[^\s]+',
            r'https?://openreview\.net/[^\s]+',
            r'https?://proceedings\.mlr\.press/[^\s]+'
        ]
        
        for pattern in paper_patterns:
            matches = re.findall(pattern, readme_content)
            paper_links.extend(matches)
        
        return list(set(paper_links))  # Remove duplicates
    
    def _analyze_structure(self, contents: List[dict]) -> dict:
        """Analyze repository structure"""
        structure = {
            "directories": [],
            "files": [],
            "languages": set(),
            "has_requirements": False,
            "has_setup": False,
            "has_dockerfile": False
        }
        
        for item in contents:
            if item["type"] == "dir":
                structure["directories"].append(item["name"])
            else:
                structure["files"].append(item["name"])
                
                # Check file extensions
                if "." in item["name"]:
                    ext = item["name"].split(".")[-1]
                    structure["languages"].add(ext)
                
                # Check for important files
                name_lower = item["name"].lower()
                if name_lower in ["requirements.txt", "environment.yml", "pipfile"]:
                    structure["has_requirements"] = True
                elif name_lower in ["setup.py", "setup.cfg"]:
                    structure["has_setup"] = True
                elif name_lower == "dockerfile":
                    structure["has_dockerfile"] = True
        
        structure["languages"] = list(structure["languages"])
        return structure
    
    def _identify_key_files(self, contents: List[dict]) -> List[str]:
        """Identify key implementation files"""
        key_files = []
        
        important_patterns = [
            r".*main\.py$",
            r".*train\.py$",
            r".*model\.py$",
            r".*network\.py$",
            r".*\.ipynb$",
            r".*demo\.py$",
            r".*test\.py$"
        ]
        
        for item in contents:
            if item["type"] == "file":
                for pattern in important_patterns:
                    if re.match(pattern, item["name"], re.IGNORECASE):
                        key_files.append(item["name"])
                        break
        
        return key_files

# Example usage and testing
if __name__ == "__main__":
    # Test without token (rate limited)
    tool = GitHubResearchTool()
    
    # Test repository search
    result = tool.execute(
        query="dataset distillation",
        search_type="repositories",
        language="python",
        limit=5
    )
    
    print("Repository search result:")
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Found {result['total_found']} repositories")
        for repo in result['repositories'][:2]:
            print(f"  - {repo['full_name']} ({repo['stars']} stars)")
    
    # Test paper-specific search
    paper_result = tool.execute(
        query="neural architecture search",
        search_type="papers",
        limit=3
    )
    
    print(f"\nPaper search result: {paper_result['status']}")
    if paper_result['status'] == 'success':
        print(f"Found {len(paper_result['paper_repositories'])} paper repositories")
