#!/usr/bin/env python3
"""
Comprehensive test of all research tools
"""

import yaml
from tools import discover_tools

def test_tool_discovery():
    """Test tool discovery system"""
    print("🔍 Testing Tool Discovery System")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Discover tools
        discovered_tools = discover_tools(config, silent=False)
        
        print(f"✅ Tool discovery successful!")
        print(f"📊 Total tools discovered: {len(discovered_tools)}")
        
        for name, tool in discovered_tools.items():
            print(f"   • {name}: {tool.description[:50]}...")
        
        return discovered_tools
        
    except Exception as e:
        print(f"❌ Tool discovery failed: {e}")
        return {}

def test_individual_tools(discovered_tools):
    """Test each tool individually"""
    print("\n🛠️ Testing Individual Tools")
    print("=" * 50)
    
    tool_results = {}
    
    for tool_name, tool in discovered_tools.items():
        print(f"\n🔧 Testing {tool_name}...")
        
        try:
            # Test based on tool type
            if tool_name == "advanced_calculate":
                result = tool.execute(
                    expression="2 + 2 * 3",
                    calculation_type="basic_arithmetic"
                )
            elif tool_name == "calculate":
                result = tool.execute(expression="sqrt(16) + log(10)")
            elif tool_name == "critical_evaluation":
                result = tool.execute(
                    content="This is a test research proposal for multimodal learning",
                    evaluation_criteria=["novelty", "feasibility"]
                )
            elif tool_name == "analyze_feasibility":
                result = tool.execute(
                    research_idea="Develop a new neural network architecture",
                    assessment_criteria=["technical_complexity", "resource_requirements"]
                )
            elif tool_name == "generate_research_ideas":
                result = tool.execute(
                    domain="machine learning",
                    focus_area="dataset distillation",
                    num_ideas=2
                )
            elif tool_name == "literature_review":
                result = tool.execute(
                    topic="dataset distillation",
                    max_papers=3
                )
            elif tool_name == "read_file":
                result = tool.execute(
                    file_path="config.yaml"
                )
            elif tool_name == "search_web":
                result = tool.execute(
                    query="machine learning research 2024",
                    num_results=2
                )
            elif tool_name == "search_papers":
                result = tool.execute(
                    query="dataset distillation",
                    max_results=3
                )
            elif tool_name == "write_file":
                result = tool.execute(
                    file_path="test_output.txt",
                    content="This is a test file created by the write_file tool."
                )
            elif tool_name == "mark_task_complete":
                result = tool.execute(
                    summary="Tool testing completed",
                    final_message="All tools tested successfully"
                )
            else:
                result = {"status": "skipped", "reason": "Unknown tool type"}
            
            if result and not isinstance(result, dict) or (isinstance(result, dict) and result.get("status") != "error"):
                print(f"   ✅ {tool_name} working")
                tool_results[tool_name] = True
            else:
                print(f"   ❌ {tool_name} failed: {result}")
                tool_results[tool_name] = False
                
        except Exception as e:
            print(f"   ❌ {tool_name} failed: {e}")
            tool_results[tool_name] = False
    
    return tool_results

def test_tool_integration():
    """Test tool integration with agent system"""
    print("\n🔗 Testing Tool Integration")
    print("=" * 50)
    
    try:
        from hybrid_agent import HybridAgent
        
        agent = HybridAgent(silent=True)
        
        # Test simple tool usage
        test_prompt = """
        Please use the calculate tool to compute 5 + 3 * 2.
        Then use the mark_task_complete tool to finish.
        """
        
        print("🔄 Testing tool integration with agent...")
        response = agent.run(test_prompt)
        
        if response and len(response) > 50:
            print(f"✅ Tool integration working!")
            print(f"📝 Response length: {len(response)} characters")
            
            # Check if tools were actually used
            if "calculate" in response.lower() or "computation" in response.lower():
                print(f"✅ Tools appear to be used correctly")
                return True
            else:
                print(f"⚠️ Tools may not be used correctly")
                return False
        else:
            print(f"❌ Tool integration failed")
            return False
            
    except Exception as e:
        print(f"❌ Tool integration test failed: {e}")
        return False

def test_knowledge_base_tools():
    """Test knowledge base specific tools"""
    print("\n📚 Testing Knowledge Base Tools")
    print("=" * 50)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        discovered_tools = discover_tools(config, silent=True)
        
        # Test read_file tool with knowledge base
        if "read_file" in discovered_tools:
            print("🔄 Testing knowledge base file access...")
            
            # Try to read a knowledge base file
            kb_files = [
                "research_knowledge_base/MMIS- Multimodal Dataset for Interior Scene Visual Generation and Recognition.pdf.md",
                "config.yaml"  # Fallback
            ]
            
            for file_path in kb_files:
                try:
                    result = discovered_tools["read_file"].execute(file_path=file_path)
                    if result and len(str(result)) > 100:
                        print(f"   ✅ Successfully read: {file_path}")
                        return True
                except Exception as e:
                    print(f"   ⚠️ Could not read {file_path}: {e}")
            
            print(f"   ❌ Could not read any knowledge base files")
            return False
        else:
            print(f"❌ read_file tool not found")
            return False
            
    except Exception as e:
        print(f"❌ Knowledge base tools test failed: {e}")
        return False

def main():
    """Run comprehensive tool testing"""
    print("🔬 COMPREHENSIVE TOOL TESTING")
    print("=" * 60)
    
    # Test 1: Tool Discovery
    discovered_tools = test_tool_discovery()
    
    if not discovered_tools:
        print("❌ Cannot proceed - tool discovery failed")
        return False
    
    # Test 2: Individual Tools
    tool_results = test_individual_tools(discovered_tools)
    
    # Test 3: Tool Integration
    integration_success = test_tool_integration()
    
    # Test 4: Knowledge Base Tools
    kb_success = test_knowledge_base_tools()
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 COMPREHENSIVE TOOL TEST SUMMARY")
    print("="*60)
    
    print(f"🔍 Tool Discovery: {'✅ PASS' if discovered_tools else '❌ FAIL'}")
    
    print(f"\n🛠️ Individual Tool Results:")
    working_tools = 0
    for tool_name, result in tool_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {tool_name:.<25} {status}")
        if result:
            working_tools += 1
    
    print(f"\n🔗 Tool Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    print(f"📚 Knowledge Base Tools: {'✅ PASS' if kb_success else '❌ FAIL'}")
    
    total_tools = len(tool_results)
    tool_success_rate = working_tools / total_tools * 100 if total_tools > 0 else 0
    
    print(f"\n📊 Overall Tool Statistics:")
    print(f"   • Total tools discovered: {len(discovered_tools)}")
    print(f"   • Working tools: {working_tools}/{total_tools}")
    print(f"   • Tool success rate: {tool_success_rate:.1f}%")
    print(f"   • Integration working: {'Yes' if integration_success else 'No'}")
    print(f"   • Knowledge base access: {'Yes' if kb_success else 'No'}")
    
    # Overall assessment
    overall_success = (
        bool(discovered_tools) and
        tool_success_rate >= 80 and
        integration_success and
        kb_success
    )
    
    if overall_success:
        print(f"\n🎉 ALL TOOL TESTS PASSED!")
        print(f"🚀 Tools ready for Enhanced Research Spiral")
        return True
    else:
        print(f"\n⚠️ TOOL ISSUES DETECTED")
        print(f"🔧 Issues to address:")
        if not discovered_tools:
            print(f"   • Tool discovery system failure")
        if tool_success_rate < 80:
            print(f"   • Low tool success rate ({tool_success_rate:.1f}%)")
        if not integration_success:
            print(f"   • Tool integration with agents not working")
        if not kb_success:
            print(f"   • Knowledge base access issues")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ TOOLS FULLY FUNCTIONAL")
        print(f"🔄 Ready for Enhanced Research Spiral execution")
    else:
        print(f"\n❌ TOOL ISSUES NEED RESOLUTION")
        print(f"🔧 Fix tool issues before running research spiral")
