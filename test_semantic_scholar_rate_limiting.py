#!/usr/bin/env python3
"""
Semantic Scholar API Rate Limiting Test

This script tests the Semantic Scholar API with proper rate limiting
to ensure the 1 request per second limit is respected.

Usage:
    python test_semantic_scholar_rate_limiting.py
"""

import time
import requests
from datetime import datetime

# Your Semantic Scholar API key
API_KEY = "zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn"
BASE_URL = "https://api.semanticscholar.org/graph/v1"

class SemanticScholarRateLimiter:
    def __init__(self, calls_per_second=0.8):
        self.calls_per_second = calls_per_second
        self.last_call = 0
    
    def wait(self):
        now = time.time()
        time_since_last = now - self.last_call
        min_interval = 1.0 / self.calls_per_second
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            print(f"⏳ Rate limiting: waiting {sleep_time:.2f} seconds...")
            time.sleep(sleep_time)
        
        self.last_call = time.time()

def test_single_request():
    """Test a single API request"""
    print("🧪 Testing single Semantic Scholar API request...")
    
    url = f"{BASE_URL}/paper/search"
    params = {
        "query": "dataset distillation",
        "limit": 3,
        "fields": "title,authors,year,abstract"
    }
    headers = {
        "Content-Type": "application/json",
        "x-api-key": API_KEY
    }
    
    try:
        start_time = time.time()
        response = requests.get(url, params=params, headers=headers, timeout=30)
        end_time = time.time()
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️  Response Time: {end_time - start_time:.2f} seconds")
        
        if response.status_code == 200:
            data = response.json()
            papers = data.get('data', [])
            print(f"✅ Success! Found {len(papers)} papers")
            for i, paper in enumerate(papers, 1):
                print(f"   {i}. {paper.get('title', 'No title')[:60]}...")
        elif response.status_code == 429:
            print("❌ Rate limit exceeded!")
            print(f"   Response: {response.text}")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_rate_limited_requests(num_requests=5):
    """Test multiple requests with rate limiting"""
    print(f"\n🧪 Testing {num_requests} rate-limited requests...")
    
    rate_limiter = SemanticScholarRateLimiter(calls_per_second=0.8)
    
    queries = [
        "dataset distillation",
        "neural network compression",
        "few shot learning",
        "meta learning",
        "transfer learning"
    ]
    
    results = []
    
    for i in range(num_requests):
        query = queries[i % len(queries)]
        print(f"\n📋 Request {i+1}/{num_requests}: '{query}'")
        
        # Apply rate limiting
        rate_limiter.wait()
        
        url = f"{BASE_URL}/paper/search"
        params = {
            "query": query,
            "limit": 2,
            "fields": "title,year"
        }
        headers = {
            "Content-Type": "application/json",
            "x-api-key": API_KEY
        }
        
        try:
            start_time = time.time()
            response = requests.get(url, params=params, headers=headers, timeout=30)
            end_time = time.time()
            
            result = {
                "request_num": i + 1,
                "query": query,
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if response.status_code == 200:
                data = response.json()
                papers = data.get('data', [])
                result["papers_found"] = len(papers)
                print(f"   ✅ Success: {len(papers)} papers found in {result['response_time']:.2f}s")
            elif response.status_code == 429:
                print(f"   ❌ Rate limit exceeded at {result['timestamp']}")
                result["error"] = "Rate limit exceeded"
            else:
                print(f"   ⚠️  Status {response.status_code} at {result['timestamp']}")
                result["error"] = f"HTTP {response.status_code}"
            
            results.append(result)
            
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
            results.append({
                "request_num": i + 1,
                "query": query,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            })
    
    return results

def analyze_results(results):
    """Analyze the test results"""
    print("\n📊 Test Results Analysis:")
    print("=" * 50)
    
    total_requests = len(results)
    successful_requests = len([r for r in results if r.get('status_code') == 200])
    rate_limit_errors = len([r for r in results if r.get('status_code') == 429])
    other_errors = len([r for r in results if 'error' in r and r.get('status_code') != 429])
    
    print(f"📈 Total Requests: {total_requests}")
    print(f"✅ Successful: {successful_requests} ({successful_requests/total_requests*100:.1f}%)")
    print(f"🚫 Rate Limited: {rate_limit_errors} ({rate_limit_errors/total_requests*100:.1f}%)")
    print(f"❌ Other Errors: {other_errors} ({other_errors/total_requests*100:.1f}%)")
    
    if successful_requests > 0:
        response_times = [r['response_time'] for r in results if 'response_time' in r]
        avg_response_time = sum(response_times) / len(response_times)
        print(f"⏱️  Average Response Time: {avg_response_time:.2f} seconds")
    
    print("\n📋 Detailed Results:")
    for result in results:
        status = "✅" if result.get('status_code') == 200 else "❌"
        query = result['query'][:20] + "..." if len(result['query']) > 20 else result['query']
        timestamp = result.get('timestamp', 'N/A')
        
        if 'response_time' in result:
            print(f"   {status} {result['request_num']:2d}. {query:25s} | {timestamp} | {result['response_time']:.2f}s")
        else:
            error = result.get('error', 'Unknown error')
            print(f"   {status} {result['request_num']:2d}. {query:25s} | {timestamp} | {error}")

def main():
    """Main test function"""
    print("🚀 Semantic Scholar API Rate Limiting Test")
    print("=" * 60)
    print(f"🔑 API Key: {API_KEY[:20]}...")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏱️  Rate Limit: 1 request per second (using 0.8 req/s for safety)")
    
    # Test 1: Single request
    test_single_request()
    
    # Wait before next test
    print("\n⏳ Waiting 3 seconds before rate limiting test...")
    time.sleep(3)
    
    # Test 2: Multiple rate-limited requests
    results = test_rate_limited_requests(5)
    
    # Analyze results
    analyze_results(results)
    
    # Recommendations
    print("\n💡 Recommendations:")
    rate_limit_errors = len([r for r in results if r.get('status_code') == 429])
    
    if rate_limit_errors == 0:
        print("   ✅ Rate limiting is working correctly!")
        print("   ✅ Safe to proceed with large-scale orchestration")
        print("   📋 Recommended settings:")
        print("      - max_concurrent: 1")
        print("      - batch_delay: 10-15 seconds")
        print("      - rate_limiter: 0.8 requests/second")
    else:
        print("   ⚠️  Rate limiting needs adjustment!")
        print("   📋 Recommended changes:")
        print("      - Increase batch_delay to 15-20 seconds")
        print("      - Reduce rate_limiter to 0.6 requests/second")
        print("      - Consider using fewer agents initially")
    
    print("\n🎯 Next Steps:")
    print("   1. If no rate limit errors: proceed with small-scale test (3-5 agents)")
    print("   2. If rate limit errors: adjust configuration and retest")
    print("   3. Monitor carefully during actual orchestration")

if __name__ == "__main__":
    main()
