#!/usr/bin/env python3
"""Test research tools with Gemini agent"""

from hybrid_agent import HybridAgent

def test_dblp_search():
    """Test DBLP search functionality"""
    print("🔍 Testing DBLP Search")
    print("=" * 30)
    
    try:
        agent = HybridAgent(silent=True)
        
        # Test DBLP search
        query = "Search for 3 recent papers on 'dataset distillation' using DBLP database"
        response = agent.run(query)
        
        if response and "dataset distillation" in response.lower():
            print("✅ DBLP search successful")
            print(f"📝 Found papers related to dataset distillation")
            return True
        else:
            print("❌ DBLP search failed or no relevant results")
            return False
            
    except Exception as e:
        print(f"❌ DBLP search failed: {e}")
        return False

def test_idea_generation():
    """Test research idea generation"""
    print("\n💡 Testing Idea Generation")
    print("=" * 30)
    
    try:
        agent = HybridAgent(silent=True)
        
        # Test idea generation with simpler prompt
        query = "Generate one research idea for improving machine learning efficiency"
        response = agent.run(query)
        
        if response and len(response) > 50:
            print("✅ Idea generation successful")
            print(f"📝 Generated research idea")
            return True
        else:
            print("❌ Idea generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Idea generation failed: {e}")
        return False

def test_web_search():
    """Test web search functionality"""
    print("\n🌐 Testing Web Search")
    print("=" * 30)
    
    try:
        agent = HybridAgent(silent=True)
        
        # Test web search
        query = "Search the web for recent news about artificial intelligence"
        response = agent.run(query)
        
        if response and len(response) > 50:
            print("✅ Web search successful")
            print(f"📝 Found web results")
            return True
        else:
            print("❌ Web search failed")
            return False
            
    except Exception as e:
        print(f"❌ Web search failed: {e}")
        return False

def main():
    """Run focused research tool tests"""
    print("🧪 Enhanced Research Framework - Tool Testing")
    print("=" * 50)
    
    # Test individual tools
    dblp_success = test_dblp_search()
    idea_success = test_idea_generation()
    web_success = test_web_search()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"✅ DBLP Search: {'PASSED' if dblp_success else 'FAILED'}")
    print(f"✅ Idea Generation: {'PASSED' if idea_success else 'FAILED'}")
    print(f"✅ Web Search: {'PASSED' if web_success else 'FAILED'}")
    
    total_passed = sum([dblp_success, idea_success, web_success])
    
    if total_passed >= 2:
        print(f"\n🎉 {total_passed}/3 tests passed! Core functionality working.")
        print("\n📋 Next Steps:")
        print("1. Test small orchestration: python small_scale_test.py")
        print("2. Run full deployment: python make_it_heavy.py")
        return True
    else:
        print(f"\n❌ Only {total_passed}/3 tests passed. Check configuration.")
        return False

if __name__ == "__main__":
    main()
