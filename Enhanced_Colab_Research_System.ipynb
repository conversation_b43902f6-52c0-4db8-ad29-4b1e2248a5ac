#%% md
# 🚀 Enhanced Make It Heavy: Large-Scale Research Orchestration System

## Overview
This notebook contains the complete enhanced Make It Heavy framework with:
- **100-agent orchestration** with sequential batch processing
- **6 advanced research tools** (semantic search, idea generation, feasibility analysis, critical thinking, advanced calculations, literature review)
- **Checkpoint management** with Google Drive integration
- **Progress monitoring** and error recovery
- **Academic-grade research capabilities**

## Quick Start
1. **Setup Environment** (Cell 1-3)
2. **Configure APIs** (Cell 4)
3. **Test Small Scale** (Cell 5)
4. **Run Full Orchestration** (Cell 6)

---
#%% md
## 📋 Step 1: Environment Setup and Dependencies
#%%
# Mount Google Drive for persistent storage
from google.colab import drive
drive.mount('/content/drive')

# Install required packages
!pip install -q google-generativeai requests beautifulsoup4 pyyaml numpy scipy matplotlib pandas ipywidgets

# Import essential libraries
import os
import json
import time
import random
import requests
import threading
import glob
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from IPython.display import display, HTML, clear_output
import ipywidgets as widgets

print("✅ Environment setup complete!")
#%% md
## ⚙️ Step 2: Configuration and API Setup
#%%
# Configuration for large-scale orchestration
CONFIG = {
    'gemini': {
        'api_key': 'AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI',  # Your actual Gemini API key
        'model': 'gemini-1.5-flash'
    },
    'dblp': {
        'base_url': 'https://dblp.org/search/publ/api',  # No API key required!
        'author_url': 'https://dblp.org/search/author/api',
        'venue_url': 'https://dblp.org/search/venue/api'
    },
    'github': {
        'token': '****************************************',  # Your actual GitHub token
        'base_url': 'https://api.github.com'
    },
    'semantic_scholar': {
        'api_key': 'zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn',  # BACKUP: Saved for later use
        'base_url': 'https://api.semanticscholar.org/graph/v1',
        'status': 'disabled_due_to_rate_limits'
    },
    'orchestrator': {
        'parallel_agents': 100,      # Total number of agents
        'max_concurrent': 2,         # RESTORED: 2 concurrent agents (DBLP has better rate limits)
        'batch_delay': 5,           # RESTORED: 5 seconds between batches (DBLP is more lenient)
        'task_timeout': 600,        # 10 minutes per agent
        'share_knowledge_base': True,
        'aggregation_strategy': 'consensus'
    },
    'knowledge_base': {
        'path': '/content/drive/MyDrive/research_knowledge_base',
        'checkpoint_path': '/content/drive/MyDrive/research_checkpoints'
    }
}

# Create necessary directories
os.makedirs(CONFIG['knowledge_base']['path'], exist_ok=True)
os.makedirs(CONFIG['knowledge_base']['checkpoint_path'], exist_ok=True)

print("⚙️ Configuration loaded. Please update API keys before proceeding.")
print(f"📁 Knowledge base: {CONFIG['knowledge_base']['path']}")
print(f"💾 Checkpoints: {CONFIG['knowledge_base']['checkpoint_path']}")
#%% md
## 🔑 Step 3: API Key Configuration

**Important**: Update the API keys in the cell above before running this cell.

- **Gemini API Key**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Semantic Scholar API Key**: Optional, get from [Semantic Scholar API](https://www.semanticscholar.org/product/api)
#%%
# Configure Gemini API
if CONFIG['gemini']['api_key'] and CONFIG['gemini']['api_key'] != 'YOUR_GEMINI_API_KEY':
    genai.configure(api_key=CONFIG['gemini']['api_key'])
    print("✅ Gemini API configured")
else:
    print("❌ Please update your Gemini API key in the configuration cell above")
    
# Test Gemini API connection
try:
    model = genai.GenerativeModel(CONFIG['gemini']['model'])
    test_response = model.generate_content("Hello, this is a test.")
    print("✅ Gemini API connection successful")
except Exception as e:
    print(f"❌ Gemini API connection failed: {e}")

# DBLP API test (no API key required!)
try:
    test_url = "https://dblp.org/search/publ/api?q=machine+learning&h=1&format=json"
    response = requests.get(test_url, timeout=10)
    if response.status_code == 200:
        data = response.json()
        total = data.get('result', {}).get('hits', {}).get('@total', 0)
        print(f"✅ DBLP API connection successful - {total} papers available for 'machine learning'")
    else:
        print(f"⚠️ DBLP API connection issue (status: {response.status_code})")
except Exception as e:
    print(f"⚠️ DBLP API test failed: {e}")

# Semantic Scholar API (disabled due to rate limits)
print("ℹ️ Semantic Scholar API disabled due to rate limits (saved as backup)")

# GitHub API test
github_token = CONFIG.get('github', {}).get('token')
if github_token and github_token != 'YOUR_GITHUB_TOKEN':
    try:
        headers = {'Authorization': f'token {github_token}', 'User-Agent': 'Research-Agent/1.0'}
        response = requests.get('https://api.github.com/user', headers=headers, timeout=10)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ GitHub API authenticated as: {user_data.get('login', 'Unknown')}")
            rate_limit = response.headers.get('X-RateLimit-Limit', 'Unknown')
            print(f"📊 GitHub API rate limit: {rate_limit} requests/hour")
        else:
            print(f"⚠️ GitHub API authentication issue (status: {response.status_code})")
    except Exception as e:
        print(f"⚠️ GitHub API test failed: {e}")
else:
    print("ℹ️ GitHub API running without authentication (60 requests/hour limit)")
    print("💡 Add your GitHub token to CONFIG['github']['token'] for higher rate limits (5000 requests/hour)")
    print("🔗 Get a token at: https://github.com/settings/tokens")

print("\n🚀 API setup complete! Ready to load the enhanced framework.")
#%% md
## 🛠️ Step 4: Load Enhanced Framework with All Research Tools

This cell loads the complete enhanced framework including:
- Base tool interface
- All 6 advanced research tools
- Enhanced agent with tool integration
- Large-scale orchestrator with batch processing
- Checkpoint management system
#%%
# ============== BASE TOOL INTERFACE ==============
from abc import ABC, abstractmethod

class BaseTool(ABC):
    """Base class for all tools"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Return the name of the tool"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Return a description of what the tool does"""
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> dict:
        """Return the parameters schema for the tool"""
        pass
    
    @abstractmethod
    def execute(self, **kwargs) -> dict:
        """Execute the tool with given parameters"""
        pass

# ============== RATE LIMITING ==============
class RateLimiter:
    def __init__(self, calls_per_second=1):
        self.calls_per_second = calls_per_second
        self.last_call = 0
    
    def wait(self):
        now = time.time()
        time_since_last = now - self.last_call
        min_interval = 1.0 / self.calls_per_second
        
        if time_since_last < min_interval:
            time.sleep(min_interval - time_since_last)
        
        self.last_call = time.time()

# Global rate limiter for Semantic Scholar API
# CRITICAL: Semantic Scholar allows only 1 request per second
# Using conservative 0.8 requests/second (1.25 second intervals) for safety
s2_rate_limiter = RateLimiter(calls_per_second=0.8)  # 1 call every 1.25 seconds

print("✅ Base framework loaded")
#%%
# ============== SEMANTIC SCHOLAR TOOL ==============
class SemanticScholarTool(BaseTool):
    """Tool for searching academic papers using Semantic Scholar API"""
    
    def __init__(self, config: dict = CONFIG):
        self.config = config
        self.base_url = config['semantic_scholar']['base_url']
        self.api_key = config['semantic_scholar'].get('api_key')
        
    @property
    def name(self) -> str:
        return "search_papers"
    
    @property
    def description(self) -> str:
        return "Search for academic papers using Semantic Scholar API"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for papers"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results",
                    "default": 10
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020-' for 2020 onwards)"
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, limit: int = 10, year_filter: str = None, **kwargs) -> dict:
        max_retries = 3
        base_delay = 2.0  # Start with 2 second delay
        
        for attempt in range(max_retries):
            try:
                # Apply rate limiting before each attempt
                s2_rate_limiter.wait()
                
                url = f"{self.base_url}/paper/search"
                params = {
                    "query": query,
                    "limit": limit,
                    "fields": "title,authors,year,abstract,citationCount,url"
                }
                
                if year_filter:
                    params["year"] = year_filter
                
                headers = {"Content-Type": "application/json"}
                if self.api_key and self.api_key != 'YOUR_SEMANTIC_SCHOLAR_API_KEY':
                    headers["x-api-key"] = self.api_key
                
                response = requests.get(url, params=params, headers=headers, timeout=30)
                
                # Handle rate limiting specifically
                if response.status_code == 429:
                    retry_delay = base_delay * (2 ** attempt)  # Exponential backoff
                    print(f"⚠️ Rate limit hit, waiting {retry_delay} seconds before retry {attempt + 1}/{max_retries}")
                    time.sleep(retry_delay)
                    continue
                
                response.raise_for_status()
                
                data = response.json()
                papers = data.get('data', [])
                
                return {
                    "status": "success",
                    "papers": papers,
                    "total_found": len(papers),
                    "query": query,
                    "attempts": attempt + 1
                }
                
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:  # Last attempt
                    return {
                        "status": "error",
                        "error": f"Request failed after {max_retries} attempts: {str(e)}",
                        "query": query
                    }
                else:
                    retry_delay = base_delay * (2 ** attempt)
                    print(f"⚠️ Request failed, retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
            except Exception as e:
                return {
                    "status": "error",
                    "error": str(e),
                    "query": query
                }
        
        return {
            "status": "error",
            "error": "Max retries exceeded",
            "query": query
        }

# ============== IDEA GENERATION TOOL ==============
class IdeaGenerationTool(BaseTool):
    """Tool for generating novel research ideas"""
    
    def __init__(self):
        self.techniques = [
            "gradient matching", "distribution matching", "feature matching",
            "meta-learning", "neural architecture search", "knowledge distillation",
            "adversarial training", "contrastive learning", "self-supervised learning"
        ]
        self.domains = [
            "computer vision", "natural language processing", "speech recognition",
            "medical imaging", "autonomous driving", "robotics", "multimodal learning"
        ]
    
    @property
    def name(self) -> str:
        return "generate_research_ideas"
    
    @property
    def description(self) -> str:
        return "Generate novel research ideas for AI/ML research"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "focus_area": {
                    "type": "string",
                    "description": "Primary research focus area",
                    "default": "dataset distillation"
                },
                "innovation_level": {
                    "type": "string",
                    "enum": ["incremental", "moderate", "breakthrough"],
                    "description": "Desired level of innovation",
                    "default": "moderate"
                },
                "num_ideas": {
                    "type": "integer",
                    "description": "Number of ideas to generate",
                    "default": 3
                }
            },
            "required": ["focus_area"]
        }
    
    def execute(self, focus_area: str, innovation_level: str = "moderate", num_ideas: int = 3, **kwargs) -> dict:
        try:
            ideas = []
            for i in range(num_ideas):
                technique = random.choice(self.techniques)
                domain = random.choice(self.domains)
                
                if innovation_level == "breakthrough":
                    title = f"Novel {technique.title()}-Based Framework for {focus_area.title()}"
                    description = f"Develop a groundbreaking approach using {technique} for {focus_area} in {domain}, addressing fundamental limitations."
                elif innovation_level == "incremental":
                    title = f"Optimized {technique.title()} for {focus_area.title()}"
                    description = f"Incremental improvements to {technique} methods in {focus_area} through optimization."
                else:
                    title = f"Enhanced {focus_area.title()} via {technique.title()}"
                    description = f"Improve {focus_area} by integrating {technique} for {domain} applications."
                
                ideas.append({
                    "id": f"idea_{i+1}",
                    "title": title,
                    "description": description,
                    "technique": technique,
                    "domain": domain,
                    "innovation_level": innovation_level
                })
            
            return {
                "status": "success",
                "ideas": ideas,
                "focus_area": focus_area,
                "total_generated": len(ideas)
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

print("✅ Research tools (1/3) loaded: DBLP Search, Idea Generation")
#%%
# ============== REPLACE SEMANTIC SCHOLAR WITH DBLP ==============
# Due to Semantic Scholar rate limiting, we're replacing it with DBLP

class DBLPSearchTool(BaseTool):
    """Tool for searching academic papers using DBLP API (no rate limits!)"""
    
    def __init__(self, config: dict = CONFIG):
        self.config = config
        self.base_url = config['dblp']['base_url']
        self.author_url = config['dblp']['author_url']
        self.venue_url = config['dblp']['venue_url']
        
    @property
    def name(self) -> str:
        return "search_papers"
    
    @property
    def description(self) -> str:
        return "Search for academic papers using DBLP (Database Systems and Logic Programming) API"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for papers (supports boolean: AND with space, OR with |)"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results (max 1000)",
                    "default": 20,
                    "maximum": 1000
                },
                "search_type": {
                    "type": "string",
                    "enum": ["publications", "authors", "venues"],
                    "description": "Type of search to perform",
                    "default": "publications"
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020' for specific year, '2020-2023' for range)"
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, limit: int = 20, search_type: str = "publications", 
                year_filter: str = None, **kwargs) -> dict:
        try:
            # Choose the appropriate endpoint
            if search_type == "authors":
                url = self.author_url
            elif search_type == "venues":
                url = self.venue_url
            else:
                url = self.base_url
            
            # Prepare parameters
            params = {
                "q": query,
                "h": min(limit, 1000),  # DBLP max is 1000
                "format": "json"
            }
            
            print(f"🔍 Searching DBLP for: '{query}' (type: {search_type}, limit: {limit})")
            
            # Make the request (no rate limiting needed!)
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            
            if search_type == "publications":
                return self._process_publications(data, query, year_filter)
            elif search_type == "authors":
                return self._process_authors(data, query)
            elif search_type == "venues":
                return self._process_venues(data, query)
                
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "error": f"DBLP API request failed: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": f"DBLP processing error: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_publications(self, data: dict, query: str, year_filter: str = None) -> dict:
        """Process publication search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            papers = hits.get("hit", [])
            
            # Ensure papers is a list
            if isinstance(papers, dict):
                papers = [papers]
            
            processed_papers = []
            
            for paper_hit in papers:
                paper_info = paper_hit.get("info", {})
                
                # Extract basic information
                title = paper_info.get("title", "No title")
                authors = paper_info.get("authors", {}).get("author", [])
                year = paper_info.get("year", "Unknown")
                venue = paper_info.get("venue", "Unknown venue")
                paper_type = paper_info.get("type", "Unknown")
                key = paper_info.get("key", "")
                
                # Process authors
                if isinstance(authors, dict):
                    authors = [authors]
                
                author_names = []
                for author in authors:
                    if isinstance(author, dict):
                        author_names.append(author.get("text", "Unknown"))
                    else:
                        author_names.append(str(author))
                
                # Apply year filter if specified
                if year_filter and year != "Unknown":
                    try:
                        paper_year = int(year)
                        if "-" in year_filter:
                            start_year, end_year = map(int, year_filter.split("-"))
                            if not (start_year <= paper_year <= end_year):
                                continue
                        else:
                            filter_year = int(year_filter)
                            if paper_year != filter_year:
                                continue
                    except (ValueError, TypeError):
                        pass  # Skip year filtering if conversion fails
                
                # Create paper entry
                paper_entry = {
                    "title": title,
                    "authors": author_names,
                    "year": year,
                    "venue": venue,
                    "type": paper_type,
                    "dblp_key": key,
                    "url": f"https://dblp.org/rec/{key}" if key else None
                }
                
                processed_papers.append(paper_entry)
            
            return {
                "status": "success",
                "papers": processed_papers,
                "total_found": len(processed_papers),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "publications"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing publications: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_authors(self, data: dict, query: str) -> dict:
        """Process author search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            authors = hits.get("hit", [])
            
            if isinstance(authors, dict):
                authors = [authors]
            
            processed_authors = []
            
            for author_hit in authors:
                author_info = author_hit.get("info", {})
                
                author_entry = {
                    "name": author_info.get("author", "Unknown"),
                    "dblp_key": author_info.get("url", ""),
                    "url": author_info.get("url", "")
                }
                
                processed_authors.append(author_entry)
            
            return {
                "status": "success",
                "authors": processed_authors,
                "total_found": len(processed_authors),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "authors"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing authors: {str(e)}",
                "query": query,
                "source": "DBLP"
            }
    
    def _process_venues(self, data: dict, query: str) -> dict:
        """Process venue search results"""
        try:
            result = data.get("result", {})
            hits = result.get("hits", {})
            total = hits.get("@total", 0)
            venues = hits.get("hit", [])
            
            if isinstance(venues, dict):
                venues = [venues]
            
            processed_venues = []
            
            for venue_hit in venues:
                venue_info = venue_hit.get("info", {})
                
                venue_entry = {
                    "name": venue_info.get("venue", "Unknown"),
                    "type": venue_info.get("type", "Unknown"),
                    "dblp_key": venue_info.get("url", ""),
                    "url": venue_info.get("url", "")
                }
                
                processed_venues.append(venue_entry)
            
            return {
                "status": "success",
                "venues": processed_venues,
                "total_found": len(processed_venues),
                "total_available": int(total),
                "query": query,
                "source": "DBLP",
                "search_type": "venues"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Error processing venues: {str(e)}",
                "query": query,
                "source": "DBLP"
            }

# Replace the SemanticScholarTool with DBLPSearchTool
# This avoids the 429 rate limit errors
SemanticScholarTool = DBLPSearchTool  # Alias for compatibility

print("✅ DBLP Search Tool loaded - No rate limits!")
print("ℹ️ Semantic Scholar functionality replaced with DBLP for better reliability")
#%%
# ============== FEASIBILITY ANALYSIS TOOL ==============
class FeasibilityAnalysisTool(BaseTool):
    """Tool for analyzing research feasibility"""
    
    @property
    def name(self) -> str:
        return "analyze_feasibility"
    
    @property
    def description(self) -> str:
        return "Analyze technical and practical feasibility of research ideas"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "research_idea": {
                    "type": "string",
                    "description": "Description of the research idea"
                },
                "timeline": {
                    "type": "string",
                    "description": "Expected timeline",
                    "default": "1 year"
                },
                "resources": {
                    "type": "object",
                    "description": "Available resources"
                }
            },
            "required": ["research_idea"]
        }
    
    def execute(self, research_idea: str, timeline: str = "1 year", resources: dict = None, **kwargs) -> dict:
        try:
            # Simple feasibility scoring
            complexity_score = self._assess_complexity(research_idea)
            resource_score = self._assess_resources(resources or {})
            timeline_score = self._assess_timeline(timeline)
            
            overall_score = (complexity_score + resource_score + timeline_score) / 3
            feasibility_level = "High" if overall_score >= 0.7 else "Moderate" if overall_score >= 0.5 else "Low"
            
            return {
                "status": "success",
                "overall_score": overall_score,
                "feasibility_level": feasibility_level,
                "breakdown": {
                    "complexity": complexity_score,
                    "resources": resource_score,
                    "timeline": timeline_score
                },
                "recommendations": self._generate_recommendations(overall_score)
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def _assess_complexity(self, idea: str) -> float:
        complex_keywords = ["novel", "breakthrough", "first", "new architecture"]
        simple_keywords = ["improve", "optimize", "extend", "apply"]
        
        idea_lower = idea.lower()
        score = 0.5
        
        for keyword in complex_keywords:
            if keyword in idea_lower:
                score -= 0.1
        for keyword in simple_keywords:
            if keyword in idea_lower:
                score += 0.1
        
        return max(0.1, min(1.0, score))
    
    def _assess_resources(self, resources: dict) -> float:
        score = 0.5
        if resources.get("team_size", 1) >= 2:
            score += 0.2
        if resources.get("compute_budget") in ["high", "unlimited"]:
            score += 0.2
        return min(1.0, score)
    
    def _assess_timeline(self, timeline: str) -> float:
        if "month" in timeline.lower():
            months = int(''.join(filter(str.isdigit, timeline)) or 6)
            return 0.9 if months >= 6 else 0.6
        return 0.8
    
    def _generate_recommendations(self, score: float) -> list:
        if score < 0.5:
            return ["Consider simplifying scope", "Seek additional resources"]
        elif score < 0.7:
            return ["Plan for challenges", "Consider phased approach"]
        else:
            return ["Highly feasible", "Consider expanding scope"]

# ============== CRITICAL THINKING TOOL ==============
class CriticalThinkingTool(BaseTool):
    """Tool for critical evaluation and peer review"""
    
    @property
    def name(self) -> str:
        return "critical_evaluation"
    
    @property
    def description(self) -> str:
        return "Perform critical evaluation and peer review simulation"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": "Research content to evaluate"
                },
                "evaluation_type": {
                    "type": "string",
                    "enum": ["methodology", "results", "idea_critique"],
                    "default": "methodology"
                }
            },
            "required": ["content"]
        }
    
    def execute(self, content: str, evaluation_type: str = "methodology", **kwargs) -> dict:
        try:
            evaluation = {
                "strengths": [],
                "weaknesses": [],
                "concerns": [],
                "recommendations": []
            }
            
            content_lower = content.lower()
            
            # Check for strengths
            if "control" in content_lower:
                evaluation["strengths"].append("Includes control conditions")
            if "statistical" in content_lower:
                evaluation["strengths"].append("Statistical analysis mentioned")
            if "baseline" in content_lower:
                evaluation["strengths"].append("Baseline comparisons included")
            
            # Check for concerns
            if "small sample" in content_lower:
                evaluation["concerns"].append("Small sample size may limit generalizability")
            if "single dataset" in content_lower:
                evaluation["concerns"].append("Single dataset evaluation insufficient")
            
            # Generate recommendations
            if not evaluation["strengths"]:
                evaluation["recommendations"].append("Strengthen experimental design")
            if evaluation["concerns"]:
                evaluation["recommendations"].append("Address identified concerns")
            
            return {
                "status": "success",
                "evaluation_type": evaluation_type,
                "evaluation": evaluation
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

print("✅ Research tools (2/3) loaded: Feasibility Analysis, Critical Thinking")
#%%
# ============== ADVANCED CALCULATION TOOL ==============
class AdvancedCalculationTool(BaseTool):
    """Tool for mathematical calculations and statistical analysis"""
    
    @property
    def name(self) -> str:
        return "advanced_calculate"
    
    @property
    def description(self) -> str:
        return "Perform advanced mathematical calculations and statistical analysis"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": ["statistical_analysis", "sample_size", "correlation"],
                    "description": "Type of calculation"
                },
                "data": {
                    "type": "array",
                    "items": {"type": "number"},
                    "description": "Numerical data"
                },
                "parameters": {
                    "type": "object",
                    "description": "Additional parameters"
                }
            },
            "required": ["operation"]
        }
    
    def execute(self, operation: str, data: list = None, parameters: dict = None, **kwargs) -> dict:
        try:
            if operation == "statistical_analysis" and data:
                import statistics
                return {
                    "status": "success",
                    "operation": operation,
                    "results": {
                        "mean": statistics.mean(data),
                        "median": statistics.median(data),
                        "std_dev": statistics.stdev(data) if len(data) > 1 else 0,
                        "min": min(data),
                        "max": max(data),
                        "count": len(data)
                    }
                }
            elif operation == "sample_size":
                effect_size = parameters.get("effect_size", 0.5) if parameters else 0.5
                alpha = parameters.get("alpha", 0.05) if parameters else 0.05
                power = parameters.get("power", 0.8) if parameters else 0.8
                
                # Simplified calculation
                sample_size = int(16 * (1.96 + 0.84)**2 / effect_size**2)
                
                return {
                    "status": "success",
                    "operation": operation,
                    "results": {
                        "required_sample_size": sample_size,
                        "effect_size": effect_size,
                        "alpha": alpha,
                        "power": power
                    }
                }
            else:
                return {"status": "error", "error": f"Operation '{operation}' not implemented"}
        except Exception as e:
            return {"status": "error", "error": str(e)}

# ============== LITERATURE REVIEW TOOL ==============
class LiteratureReviewTool(BaseTool):
    """Tool for automated literature review and gap analysis"""
    
    @property
    def name(self) -> str:
        return "literature_review"
    
    @property
    def description(self) -> str:
        return "Perform automated literature review analysis and gap identification"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "papers_data": {
                    "type": "array",
                    "description": "List of papers to analyze"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["gap_analysis", "trend_analysis", "thematic_review"],
                    "default": "gap_analysis"
                }
            },
            "required": ["papers_data"]
        }
    
    def execute(self, papers_data: list, analysis_type: str = "gap_analysis", **kwargs) -> dict:
        try:
            if analysis_type == "gap_analysis":
                gaps = self._identify_gaps(papers_data)
                return {
                    "status": "success",
                    "analysis_type": analysis_type,
                    "identified_gaps": gaps,
                    "papers_analyzed": len(papers_data)
                }
            elif analysis_type == "thematic_review":
                themes = self._extract_themes(papers_data)
                return {
                    "status": "success",
                    "analysis_type": analysis_type,
                    "themes": themes,
                    "papers_analyzed": len(papers_data)
                }
            else:
                return {"status": "error", "error": f"Analysis type '{analysis_type}' not implemented"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def _identify_gaps(self, papers_data: list) -> dict:
        gaps = {"theoretical_gaps": [], "empirical_gaps": [], "methodological_gaps": []}
        
        for paper in papers_data:
            title = paper.get("title", "").lower()
            abstract = paper.get("abstract", "").lower()
            
            if "theoretical" not in abstract:
                gaps["theoretical_gaps"].append("Limited theoretical foundation")
            if "experiment" not in abstract:
                gaps["empirical_gaps"].append("Insufficient experimental validation")
            if "method" not in abstract:
                gaps["methodological_gaps"].append("Unclear methodology")
        
        return gaps
    
    def _extract_themes(self, papers_data: list) -> dict:
        themes = {}
        common_terms = ["learning", "neural", "deep", "machine", "algorithm", "model"]
        
        for paper in papers_data:
            title = paper.get("title", "").lower()
            for term in common_terms:
                if term in title:
                    themes[term] = themes.get(term, 0) + 1
        
        return themes

# ============== FILE TOOLS ==============
class ReadFileTool(BaseTool):
    @property
    def name(self) -> str:
        return "read_file"
    
    @property
    def description(self) -> str:
        return "Read content from a file"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "file_path": {"type": "string", "description": "Path to the file to read"}
            },
            "required": ["file_path"]
        }
    
    def execute(self, file_path: str, **kwargs) -> dict:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"status": "success", "content": content, "file_path": file_path}
        except Exception as e:
            return {"status": "error", "error": str(e), "file_path": file_path}

class WriteFileTool(BaseTool):
    @property
    def name(self) -> str:
        return "write_file"
    
    @property
    def description(self) -> str:
        return "Write content to a file"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "file_path": {"type": "string", "description": "Path to write the file"},
                "content": {"type": "string", "description": "Content to write"}
            },
            "required": ["file_path", "content"]
        }
    
    def execute(self, file_path: str, content: str, **kwargs) -> dict:
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return {"status": "success", "file_path": file_path, "bytes_written": len(content)}
        except Exception as e:
            return {"status": "error", "error": str(e), "file_path": file_path}

print("✅ Research tools (3/3) loaded: Advanced Calculations, Literature Review, File Tools")
#%%
# ============== GITHUB RESEARCH TOOL ==============
import base64
import re

class GitHubResearchTool(BaseTool):
    """Tool for searching and analyzing GitHub repositories related to research papers"""
    
    def __init__(self, config: dict = CONFIG):
        self.config = config
        self.github_token = config.get('github', {}).get('token')
        self.base_url = "https://api.github.com"
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "Research-Agent/1.0"
        }
        
        if self.github_token and self.github_token != 'YOUR_GITHUB_TOKEN':
            self.headers["Authorization"] = f"token {self.github_token}"
            print("✅ GitHub API authenticated with token")
        else:
            print("⚠️ GitHub API running without authentication (rate limited to 60 requests/hour)")
    
    @property
    def name(self) -> str:
        return "search_github_research"
    
    @property
    def description(self) -> str:
        return "Search GitHub repositories for research paper implementations and analyze code"
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query (paper title, research topic, or technique)"
                },
                "search_type": {
                    "type": "string",
                    "enum": ["repositories", "code", "papers"],
                    "description": "Type of search: repositories (general), code (specific files), papers (paper implementations)",
                    "default": "papers"
                },
                "language": {
                    "type": "string",
                    "description": "Programming language filter (python, javascript, etc.)",
                    "default": "python"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of results",
                    "default": 5,
                    "maximum": 20
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, search_type: str = "papers", language: str = "python", 
                limit: int = 5, **kwargs) -> dict:
        try:
            print(f"🔍 Searching GitHub for {search_type}: '{query}' (language: {language})")
            
            if search_type == "papers":
                return self._search_paper_implementations(query, language, limit)
            elif search_type == "repositories":
                return self._search_repositories(query, language, limit)
            elif search_type == "code":
                return self._search_code_files(query, language, limit)
            else:
                return {"status": "error", "error": f"Unknown search type: {search_type}"}
                
        except Exception as e:
            return {
                "status": "error",
                "error": f"GitHub search failed: {str(e)}",
                "query": query
            }
    
    def _search_paper_implementations(self, query: str, language: str, limit: int) -> dict:
        """Search for repositories that implement research papers"""
        
        # Enhanced search query for paper implementations
        search_terms = [
            f"{query} implementation",
            f"{query} paper",
            f"{query} pytorch",
            f"{query} tensorflow",
            f"{query} official"
        ]
        
        search_query = f"({' OR '.join(search_terms)})"
        if language:
            search_query += f" language:{language}"
        
        url = f"{self.base_url}/search/repositories"
        params = {
            "q": search_query,
            "sort": "stars",
            "order": "desc",
            "per_page": min(limit * 2, 30)  # Get more to filter
        }
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        
        if response.status_code == 403:
            return {
                "status": "error",
                "error": "GitHub API rate limit exceeded. Please add a GitHub token to CONFIG['github']['token']",
                "query": query
            }
        
        response.raise_for_status()
        data = response.json()
        
        # Filter and analyze repositories
        paper_repos = []
        for repo in data.get("items", []):
            # Check if this looks like a paper implementation
            paper_score = self._calculate_paper_score(repo, query)
            
            if paper_score > 0.3:  # Threshold for paper relevance
                repo_info = {
                    "name": repo["name"],
                    "full_name": repo["full_name"],
                    "description": repo.get("description", "No description"),
                    "url": repo["html_url"],
                    "stars": repo["stargazers_count"],
                    "forks": repo["forks_count"],
                    "language": repo.get("language", "Unknown"),
                    "updated_at": repo["updated_at"],
                    "topics": repo.get("topics", []),
                    "paper_relevance_score": paper_score
                }
                
                # Get README and analyze
                readme_info = self._get_repository_info(repo["full_name"])
                repo_info.update(readme_info)
                
                paper_repos.append(repo_info)
                
                if len(paper_repos) >= limit:
                    break
        
        # Sort by relevance score
        paper_repos.sort(key=lambda x: x["paper_relevance_score"], reverse=True)
        
        return {
            "status": "success",
            "query": query,
            "total_found": len(paper_repos),
            "repositories": paper_repos,
            "search_type": "paper_implementations"
        }
    
    def _search_repositories(self, query: str, language: str, limit: int) -> dict:
        """General repository search"""
        search_query = query
        if language:
            search_query += f" language:{language}"
        
        url = f"{self.base_url}/search/repositories"
        params = {
            "q": search_query,
            "sort": "stars",
            "order": "desc",
            "per_page": limit
        }
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        repositories = []
        for repo in data.get("items", [])[:limit]:
            repo_info = {
                "name": repo["name"],
                "full_name": repo["full_name"],
                "description": repo.get("description", ""),
                "url": repo["html_url"],
                "stars": repo["stargazers_count"],
                "forks": repo["forks_count"],
                "language": repo.get("language", "Unknown")
            }
            repositories.append(repo_info)
        
        return {
            "status": "success",
            "query": query,
            "total_found": data.get("total_count", 0),
            "repositories": repositories,
            "search_type": "repositories"
        }
    
    def _search_code_files(self, query: str, language: str, limit: int) -> dict:
        """Search for specific code implementations"""
        search_query = query
        if language:
            search_query += f" language:{language}"
        
        url = f"{self.base_url}/search/code"
        params = {
            "q": search_query,
            "per_page": limit
        }
        
        response = requests.get(url, headers=self.headers, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        code_files = []
        for item in data.get("items", [])[:limit]:
            file_info = {
                "name": item["name"],
                "path": item["path"],
                "repository": item["repository"]["full_name"],
                "url": item["html_url"],
                "language": item.get("language", "Unknown")
            }
            code_files.append(file_info)
        
        return {
            "status": "success",
            "query": query,
            "total_found": data.get("total_count", 0),
            "code_files": code_files,
            "search_type": "code_files"
        }

print("✅ GitHub Research Tool loaded")
#%%
# ============== GITHUB TOOL HELPER METHODS ==============
# Add helper methods to GitHubResearchTool class

def _calculate_paper_score(self, repo: dict, query: str) -> float:
    """Calculate how likely a repository is to be a paper implementation"""
    score = 0.0
    
    description = (repo.get("description") or "").lower()
    name = repo["name"].lower()
    query_lower = query.lower()
    
    # Check for paper-related keywords
    paper_keywords = ["paper", "implementation", "official", "reproduce", "replication", 
                     "arxiv", "pytorch", "tensorflow", "research"]
    
    for keyword in paper_keywords:
        if keyword in description:
            score += 0.15
        if keyword in name:
            score += 0.1
    
    # Check for query terms in name/description
    query_words = query_lower.split()
    for word in query_words:
        if len(word) > 3:  # Skip short words
            if word in description:
                score += 0.2
            if word in name:
                score += 0.15
    
    # Boost score for popular repositories
    stars = repo["stargazers_count"]
    if stars > 1000:
        score += 0.2
    elif stars > 100:
        score += 0.1
    elif stars > 10:
        score += 0.05
    
    # Check topics
    topics = repo.get("topics", [])
    research_topics = ["machine-learning", "deep-learning", "computer-vision", 
                      "natural-language-processing", "research", "paper", "pytorch", "tensorflow"]
    
    for topic in topics:
        if topic in research_topics:
            score += 0.1
    
    return min(score, 1.0)

def _get_repository_info(self, full_name: str) -> dict:
    """Get additional repository information including README"""
    info = {
        "readme_preview": None,
        "paper_links": [],
        "key_files": [],
        "has_requirements": False
    }
    
    try:
        # Get repository contents
        contents_url = f"{self.base_url}/repos/{full_name}/contents"
        response = requests.get(contents_url, headers=self.headers, timeout=15)
        
        if response.status_code == 200:
            contents = response.json()
            
            # Look for important files
            for item in contents:
                name_lower = item["name"].lower()
                
                # Check for README
                if name_lower.startswith("readme"):
                    readme_content = self._get_file_content(full_name, item["name"])
                    if readme_content:
                        info["readme_preview"] = readme_content[:500] + "..." if len(readme_content) > 500 else readme_content
                        info["paper_links"] = self._extract_paper_links(readme_content)
                
                # Check for requirements
                if name_lower in ["requirements.txt", "environment.yml", "setup.py"]:
                    info["has_requirements"] = True
                
                # Identify key files
                if any(pattern in name_lower for pattern in ["main", "train", "model", "network"]):
                    info["key_files"].append(item["name"])
    
    except Exception as e:
        info["error"] = f"Failed to get repo info: {str(e)}"
    
    return info

def _get_file_content(self, full_name: str, file_path: str) -> str:
    """Get content of a specific file"""
    try:
        url = f"{self.base_url}/repos/{full_name}/contents/{file_path}"
        response = requests.get(url, headers=self.headers, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("encoding") == "base64" and data.get("size", 0) < 50000:  # Max 50KB
                content = base64.b64decode(data["content"]).decode("utf-8", errors="ignore")
                return content
    except Exception:
        pass
    
    return None

def _extract_paper_links(self, content: str) -> list:
    """Extract paper links from text content"""
    links = []
    
    # arXiv links
    arxiv_pattern = r'https?://arxiv\.org/(?:abs/|pdf/)?(\d{4}\.\d{4,5})'
    arxiv_matches = re.findall(arxiv_pattern, content)
    links.extend([f"https://arxiv.org/abs/{match}" for match in arxiv_matches])
    
    # Other paper links
    paper_patterns = [
        r'https?://[^\s]+\.pdf',
        r'https?://papers\.nips\.cc/[^\s]+',
        r'https?://openreview\.net/[^\s]+'
    ]
    
    for pattern in paper_patterns:
        matches = re.findall(pattern, content)
        links.extend(matches)
    
    return list(set(links))  # Remove duplicates

# Add methods to the GitHubResearchTool class
GitHubResearchTool._calculate_paper_score = _calculate_paper_score
GitHubResearchTool._get_repository_info = _get_repository_info
GitHubResearchTool._get_file_content = _get_file_content
GitHubResearchTool._extract_paper_links = _extract_paper_links

print("✅ GitHub Research Tool helper methods loaded")
#%%
# ============== COMPLETE ORCHESTRATOR WITH TOOLS ==============
import google.generativeai as genai
import json
import re
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

class CheckpointManager:
    def __init__(self, checkpoint_dir: str = None):
        if checkpoint_dir is None:
            checkpoint_dir = CONFIG['knowledge_base']['checkpoint_path']
        self.checkpoint_dir = checkpoint_dir
        os.makedirs(checkpoint_dir, exist_ok=True)
        
    def save_checkpoint(self, session_id: str, data: dict):
        """Save checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            with open(checkpoint_file, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"💾 Checkpoint saved: {session_id}")
        except Exception as e:
            print(f"❌ Failed to save checkpoint: {e}")
    
    def load_checkpoint(self, session_id: str) -> dict:
        """Load checkpoint data"""
        checkpoint_file = os.path.join(self.checkpoint_dir, f"{session_id}_checkpoint.json")
        try:
            if os.path.exists(checkpoint_file):
                with open(checkpoint_file, 'r') as f:
                    data = json.load(f)
                print(f"📂 Checkpoint loaded: {session_id}")
                return data
        except Exception as e:
            print(f"❌ Failed to load checkpoint: {e}")
        return {}
    
    def list_checkpoints(self) -> List[str]:
        """List available checkpoints"""
        try:
            files = glob.glob(os.path.join(self.checkpoint_dir, "*_checkpoint.json"))
            return [os.path.basename(f).replace("_checkpoint.json", "") for f in files]
        except:
            return []

class GeminiOrchestratorWithTools:
    def __init__(self, config: dict):
        self.config = config
        genai.configure(api_key=config['gemini']['api_key'])
        self.model = genai.GenerativeModel(config['gemini']['model'])
        
        # Initialize all tools
        self.tools = {
            'search_papers': DBLPSearchTool(config),
            'search_github_research': GitHubResearchTool(config),  # NEW: GitHub research tool
            'generate_research_ideas': IdeaGenerationTool(),
            'analyze_feasibility': FeasibilityAnalysisTool(),
            'critical_evaluation': CriticalThinkingTool(),
            'advanced_calculate': AdvancedCalculationTool(),
            'literature_review': LiteratureReviewTool(),
            'read_file': ReadFileTool(),
            'write_file': WriteFileTool()
        }
        
        # Orchestration settings
        self.num_agents = config['orchestrator']['parallel_agents']
        self.max_concurrent = config['orchestrator']['max_concurrent']
        self.batch_delay = config['orchestrator']['batch_delay']
        self.task_timeout = config['orchestrator']['task_timeout']
        
        # Checkpoint management
        self.checkpoint_manager = CheckpointManager()
        self.session_id = None
        
        print(f"🤖 Orchestrator initialized with {len(self.tools)} tools")
        print(f"⚙️ Configuration: {self.num_agents} agents, {self.max_concurrent} concurrent, {self.batch_delay}s delay")
    
    def set_session_id(self, session_id: str):
        self.session_id = session_id
    
    def run_agent_with_tools(self, agent_id: int, subtask: str) -> Dict[str, Any]:
        """Run a single agent with access to all research tools"""
        try:
            start_time = time.time()
            
            # Create enhanced prompt with tool descriptions
            tool_descriptions = "\n".join([
                f"- {name}: {tool.description}" 
                for name, tool in self.tools.items()
            ])
            
            enhanced_prompt = f"""
You are a research assistant with access to advanced research tools. Your task: {subtask}

Available tools:
{tool_descriptions}

Instructions:
1. Use the search_papers tool to find relevant academic papers
2. Use generate_research_ideas for novel idea creation
3. Use analyze_feasibility to assess technical viability
4. Use critical_evaluation for unbiased analysis
5. Use advanced_calculate for statistical analysis
6. Use literature_review for gap analysis

Provide a comprehensive analysis using multiple tools where appropriate.
Format your response as a detailed research report.
"""
            
            # Generate response
            response = self.model.generate_content(enhanced_prompt)
            result_text = response.text
            
            # Simulate tool usage based on subtask content
            tools_used = []
            
            # Use DBLP search for paper-related queries
            if any(word in subtask.lower() for word in ["papers", "literature", "research", "study"]):
                try:
                    search_query = " ".join(subtask.split()[:4])  # First 4 words
                    search_result = self.tools['search_papers'].execute(
                        query=search_query,
                        limit=5
                    )
                    if search_result.get('status') == 'success':
                        tools_used.append(f"📚 Found {search_result.get('total_found', 0)} papers via DBLP")
                        result_text += f"\n\n📚 Academic Papers Found: {search_result.get('total_found', 0)} papers from DBLP database"
                except Exception as e:
                    tools_used.append(f"📚 Paper search failed: {str(e)}")
            
            # Use GitHub search for implementation-related queries
            if any(word in subtask.lower() for word in ["implementation", "code", "github", "reproduce", "official"]):
                try:
                    search_query = " ".join(subtask.split()[:4])  # First 4 words
                    github_result = self.tools['search_github_research'].execute(
                        query=search_query,
                        search_type="papers",
                        limit=3
                    )
                    if github_result.get('status') == 'success':
                        repos_found = len(github_result.get('repositories', []))
                        tools_used.append(f"💻 Found {repos_found} GitHub implementations")
                        result_text += f"\n\n💻 GitHub Implementations: {repos_found} repositories with code implementations"
                except Exception as e:
                    tools_used.append(f"💻 GitHub search failed: {str(e)}")
            
            # Use idea generation for creative tasks
            if any(word in subtask.lower() for word in ["idea", "novel", "innovative", "creative"]):
                try:
                    idea_result = self.tools['generate_research_ideas'].execute(
                        focus_area=subtask,
                        num_ideas=2,
                        innovation_level="moderate"
                    )
                    if idea_result.get('status') == 'success':
                        tools_used.append(f"💡 Generated {len(idea_result.get('ideas', []))} research ideas")
                        result_text += f"\n\n💡 Research Ideas Generated: {len(idea_result.get('ideas', []))} novel concepts"
                except Exception as e:
                    tools_used.append(f"💡 Idea generation failed: {str(e)}")
            
            # Use feasibility analysis for assessment tasks
            if any(word in subtask.lower() for word in ["feasible", "viable", "practical", "assess"]):
                try:
                    feasibility_result = self.tools['analyze_feasibility'].execute(
                        research_idea=subtask,
                        timeline="1 year"
                    )
                    if feasibility_result.get('status') == 'success':
                        level = feasibility_result.get('feasibility_level', 'Unknown')
                        tools_used.append(f"🎯 Feasibility: {level}")
                        result_text += f"\n\n🎯 Feasibility Assessment: {level} feasibility level"
                except Exception as e:
                    tools_used.append(f"🎯 Feasibility analysis failed: {str(e)}")
            
            execution_time = time.time() - start_time
            
            # Add tools used summary
            if tools_used:
                result_text += f"\n\n🔧 Tools Used: {', '.join(tools_used)}"
            
            return {
                "agent_id": agent_id,
                "status": "success",
                "response": result_text,
                "execution_time": execution_time,
                "subtask": subtask,
                "tools_used": tools_used
            }
            
        except Exception as e:
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "execution_time": time.time() - start_time if 'start_time' in locals() else 0,
                "subtask": subtask
            }

print("✅ Complete orchestrator with tools loaded")
#%%
# ============== ORCHESTRATOR METHODS ==============
# Add remaining methods to the GeminiOrchestratorWithTools class

def decompose_task(self, user_input: str, num_agents: int) -> List[str]:
    """Decompose user input into subtasks for multiple agents"""
    prompt = f"""
Generate exactly {num_agents} different, specific research questions that would help thoroughly analyze: {user_input}

Each question should approach the topic from a different angle:
- Literature review and background
- Technical methodology analysis
- Novel idea generation
- Feasibility assessment
- Critical evaluation
- Comparative analysis
- Future directions
- Implementation challenges
- Performance metrics
- Related work analysis

Return ONLY a JSON array of exactly {num_agents} questions.
Example: ["What are the key papers on X?", "What are novel approaches to X?", "What are the challenges in X?"]
"""
    
    try:
        response = self.model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Extract JSON array
        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
        if json_match:
            questions = json.loads(json_match.group())
            if len(questions) == num_agents:
                return questions
        
        # Fallback: generate simple questions
        return [f"Analyze aspect {i+1} of {user_input}" for i in range(num_agents)]
        
    except Exception as e:
        print(f"⚠️ Task decomposition failed: {e}")
        return [f"Research question {i+1} about {user_input}" for i in range(num_agents)]

def aggregate_results(self, agent_results: List[Dict]) -> str:
    """Aggregate results from multiple agents"""
    successful_results = [r for r in agent_results if r['status'] == 'success']
    
    if not successful_results:
        return "No successful results to synthesize."
    
    # Combine all responses
    combined_responses = "\n\n".join([
        f"Agent {r['agent_id']} Analysis:\n{r['response']}"
        for r in successful_results
    ])
    
    synthesis_prompt = f"""
You have {len(successful_results)} different AI agents that analyzed a research query from different perspectives.

Here are their responses:

{combined_responses}

Synthesize all these perspectives into one comprehensive, well-structured research report that:
1. Combines the key insights from all agents
2. Identifies common themes and patterns
3. Resolves any contradictions by explaining different viewpoints
4. Provides a complete picture of the research topic
5. Includes actionable recommendations
6. Is well-organized with clear sections

Format as a professional research report with sections and bullet points.
"""
    
    try:
        response = self.model.generate_content(synthesis_prompt)
        return response.text
    except Exception as e:
        return f"Synthesis failed: {str(e)}\n\nRaw results:\n{combined_responses}"

def run_sequential_batch_orchestration(self, user_input: str, progress_callback=None) -> Dict[str, Any]:
    """Run large-scale sequential batch orchestration with checkpointing"""
    
    if not self.session_id:
        self.session_id = f"session_{int(time.time())}"
    
    print(f"🚀 Starting {self.num_agents}-agent orchestration for: {user_input}")
    print(f"📊 Configuration: {self.max_concurrent} concurrent, {self.batch_delay}s batch delay")
    
    # Check for existing checkpoint
    checkpoint_data = self.checkpoint_manager.load_checkpoint(self.session_id)
    
    if checkpoint_data:
        print(f"📂 Resuming from checkpoint with {len(checkpoint_data.get('completed_agents', []))} completed agents")
        completed_agents = checkpoint_data.get('completed_agents', [])
        agent_results = checkpoint_data.get('agent_results', [])
        subtasks = checkpoint_data.get('subtasks', [])
    else:
        print("🔄 Generating research questions...")
        subtasks = self.decompose_task(user_input, self.num_agents)
        completed_agents = []
        agent_results = []
        
        # Save initial checkpoint
        self.checkpoint_manager.save_checkpoint(self.session_id, {
            'user_input': user_input,
            'subtasks': subtasks,
            'completed_agents': completed_agents,
            'agent_results': agent_results,
            'total_agents': self.num_agents
        })
    
    # Calculate batches
    remaining_agents = [i for i in range(self.num_agents) if i not in completed_agents]
    total_batches = (len(remaining_agents) + self.max_concurrent - 1) // self.max_concurrent
    
    print(f"📋 Processing {len(remaining_agents)} remaining agents in {total_batches} batches")
    
    # Process agents in batches
    for batch_idx in range(total_batches):
        batch_start = batch_idx * self.max_concurrent
        batch_end = min(batch_start + self.max_concurrent, len(remaining_agents))
        batch_agents = remaining_agents[batch_start:batch_end]
        
        print(f"\n🔄 Batch {batch_idx + 1}/{total_batches}: Processing agents {batch_agents}")
        
        # Run batch concurrently
        batch_results = []
        with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            future_to_agent = {
                executor.submit(self.run_agent_with_tools, agent_id, subtasks[agent_id]): agent_id
                for agent_id in batch_agents
            }
            
            for future in as_completed(future_to_agent, timeout=self.task_timeout + 100):
                try:
                    result = future.result()
                    batch_results.append(result)
                    agent_id = result['agent_id']
                    completed_agents.append(agent_id)
                    
                    print(f"  ✅ Agent {agent_id} completed ({result['status']}) - {result['execution_time']:.1f}s")
                    
                    # Update progress
                    if progress_callback:
                        progress_callback(len(completed_agents), self.num_agents, result)
                        
                except Exception as e:
                    agent_id = future_to_agent[future]
                    error_result = {
                        "agent_id": agent_id,
                        "subtask": subtasks[agent_id],
                        "response": f"Batch execution error: {str(e)}",
                        "status": "error",
                        "execution_time": self.task_timeout
                    }
                    batch_results.append(error_result)
                    completed_agents.append(agent_id)
                    print(f"  ❌ Agent {agent_id} failed: {str(e)}")
        
        # Add batch results to overall results
        agent_results.extend(batch_results)
        
        # Save checkpoint after each batch
        checkpoint_data = {
            'user_input': user_input,
            'subtasks': subtasks,
            'completed_agents': completed_agents,
            'agent_results': agent_results,
            'total_agents': self.num_agents,
            'batch_completed': batch_idx + 1,
            'total_batches': total_batches
        }
        self.checkpoint_manager.save_checkpoint(self.session_id, checkpoint_data)
        
        # Batch delay (except for last batch)
        if batch_idx < total_batches - 1:
            print(f"⏳ Waiting {self.batch_delay}s before next batch...")
            time.sleep(self.batch_delay)
    
    print(f"\n🎉 All {self.num_agents} agents completed!")
    print(f"📊 Success: {len([r for r in agent_results if r['status'] == 'success'])}")
    print(f"❌ Errors: {len([r for r in agent_results if r['status'] == 'error'])}")
    
    # Aggregate results
    print("🔀 Synthesizing results...")
    final_synthesis = self.aggregate_results(agent_results)
    
    # Save final results
    final_data = checkpoint_data.copy()
    final_data['final_synthesis'] = final_synthesis
    final_data['completion_time'] = datetime.now().isoformat()
    self.checkpoint_manager.save_checkpoint(f"{self.session_id}_final", final_data)
    
    return {
        'user_input': user_input,
        'total_agents': self.num_agents,
        'agent_results': agent_results,
        'final_synthesis': final_synthesis,
        'session_id': self.session_id,
        'statistics': {
            'completed': len([r for r in agent_results if r['status'] == 'success']),
            'errors': len([r for r in agent_results if r['status'] == 'error']),
            'total_duration': sum(r['execution_time'] for r in agent_results),
            'avg_duration': sum(r['execution_time'] for r in agent_results) / len(agent_results) if agent_results else 0
        }
    }

# Add methods to the class
GeminiOrchestratorWithTools.decompose_task = decompose_task
GeminiOrchestratorWithTools.aggregate_results = aggregate_results
GeminiOrchestratorWithTools.run_sequential_batch_orchestration = run_sequential_batch_orchestration

print("✅ Orchestrator methods added")
#%% md
## 🎯 **INTERACTIVE RESEARCH INTERFACE**

**THIS IS WHERE YOU ENTER YOUR RESEARCH QUERY AND START THE ORCHESTRATION!**

Features:
- 📝 **Enter your research query** in the text area
- 🎛️ **Choose orchestration mode** (Test/Medium/Full scale)
- ⚙️ **Adjust settings** (concurrent agents, delays)
- 📊 **Monitor progress** in real-time
- 📋 **View results** and final synthesis
- 💾 **Automatic checkpointing** for long runs
#%%
# ============== INTERACTIVE RESEARCH GUI ==============
import ipywidgets as widgets
from IPython.display import display, clear_output, HTML
import threading

class ResearchInterface:
    def __init__(self):
        self.orchestrator = None
        self.current_session = None
        self.is_running = False
        self.setup_widgets()
        
    def setup_widgets(self):
        """Setup the interactive interface widgets"""
        
        # Title
        self.title = widgets.HTML(
            value="<h2>🚀 Enhanced Make It Heavy: Large-Scale Research Orchestration</h2>"
        )
        
        # Research query input
        self.query_input = widgets.Textarea(
            value="multimodal dataset distillation for tri-modal learning",
            placeholder="Enter your research query here...",
            description="Research Query:",
            layout=widgets.Layout(width='100%', height='100px'),
            style={'description_width': '120px'}
        )
        
        # Mode selection
        self.mode_selector = widgets.RadioButtons(
            options=[
                ('🧪 Test Mode (5 agents) - ~15 minutes', 5),
                ('📊 Medium Scale (25 agents) - ~1.5 hours', 25),
                ('🚀 Full Scale (100 agents) - ~5 hours', 100)
            ],
            value=5,
            description="Mode:",
            layout=widgets.Layout(width='100%'),
            style={'description_width': '120px'}
        )
        
        # Advanced settings
        self.concurrent_slider = widgets.IntSlider(
            value=2,
            min=1,
            max=3,
            step=1,
            description="Concurrent:",
            layout=widgets.Layout(width='300px'),
            style={'description_width': '80px'}
        )
        
        self.delay_slider = widgets.IntSlider(
            value=5,
            min=2,
            max=15,
            step=1,
            description="Delay (s):",
            layout=widgets.Layout(width='300px'),
            style={'description_width': '80px'}
        )
        
        # Control buttons
        self.start_button = widgets.Button(
            description="🚀 START RESEARCH",
            button_style='success',
            layout=widgets.Layout(width='200px', height='50px')
        )
        
        self.stop_button = widgets.Button(
            description="⏹️ STOP",
            button_style='danger',
            layout=widgets.Layout(width='100px', height='50px'),
            disabled=True
        )
        
        # Progress monitoring
        self.progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Progress:',
            bar_style='info',
            layout=widgets.Layout(width='100%')
        )
        
        self.status_display = widgets.HTML(
            value="<b>Status:</b> 🟢 Ready to start - Enter your research query above and click START",
            layout=widgets.Layout(width='100%')
        )
        
        # Results display
        self.results_output = widgets.Output(
            layout=widgets.Layout(width='100%', height='500px', border='2px solid #ddd', padding='10px')
        )
        
        # Event handlers
        self.start_button.on_click(self.start_orchestration)
        self.stop_button.on_click(self.stop_orchestration)
    
    def display_interface(self):
        """Display the complete interface"""
        
        # Main interface layout
        interface = widgets.VBox([
            self.title,
            
            widgets.HTML("<h3>📝 Research Configuration</h3>"),
            self.query_input,
            self.mode_selector,
            
            widgets.HTML("<h3>⚙️ Advanced Settings</h3>"),
            widgets.HBox([self.concurrent_slider, self.delay_slider]),
            
            widgets.HTML("<h3>🎮 Control Panel</h3>"),
            widgets.HBox([self.start_button, self.stop_button]),
            
            widgets.HTML("<h3>📊 Progress Monitoring</h3>"),
            self.progress_bar,
            self.status_display,
            
            widgets.HTML("<h3>📋 Live Results & Progress</h3>"),
            self.results_output
        ])
        
        display(interface)
        
        # Initial welcome message
        with self.results_output:
            print("🎯 WELCOME TO ENHANCED MAKE IT HEAVY!")
            print("=" * 60)
            print("🚀 Large-Scale Research Orchestration System")
            print("\n✨ Features:")
            print("  📚 DBLP Academic Search (no rate limits!)")
            print("  🧠 6 Advanced Research Tools")
            print("  💾 Automatic Checkpoint Saving")
            print("  📊 Real-time Progress Monitoring")
            print("  🔄 Session Resumption")
            print("\n📋 Quick Start:")
            print("  1. ✏️  Enter your research query above")
            print("  2. 🎛️  Choose mode (start with Test Mode)")
            print("  3. 🚀 Click 'START RESEARCH'")
            print("  4. 👀 Watch the magic happen!")
            print("\n" + "=" * 60)
            print("Ready to revolutionize your research! 🔬✨")

print("✅ Research interface created")
#%%
# ============== ORCHESTRATION LOGIC ==============
# Add orchestration methods to ResearchInterface

def start_orchestration(self, button):
    """Start the research orchestration"""
    query = self.query_input.value.strip()
    num_agents = self.mode_selector.value
    max_concurrent = self.concurrent_slider.value
    batch_delay = self.delay_slider.value
    
    if not query:
        with self.results_output:
            print("\n❌ ERROR: Please enter a research query!")
        return
    
    if self.is_running:
        with self.results_output:
            print("\n⚠️ Orchestration already running!")
        return
    
    # Update UI state
    self.is_running = True
    self.start_button.disabled = True
    self.stop_button.disabled = False
    self.progress_bar.max = num_agents
    self.progress_bar.value = 0
    
    # Clear results
    self.results_output.clear_output()
    
    with self.results_output:
        print("🚀 STARTING RESEARCH ORCHESTRATION")
        print("=" * 60)
        print(f"📝 Research Query: {query}")
        print(f"🤖 Number of Agents: {num_agents}")
        print(f"⚙️ Configuration: {max_concurrent} concurrent, {batch_delay}s delay")
        print(f"⏱️ Estimated Duration: {self._estimate_duration(num_agents)} minutes")
        print("\n🔄 Initializing orchestrator...")
    
    # Start orchestration in background thread
    def run_orchestration():
        try:
            # Update configuration
            config = CONFIG.copy()
            config['orchestrator']['parallel_agents'] = num_agents
            config['orchestrator']['max_concurrent'] = max_concurrent
            config['orchestrator']['batch_delay'] = batch_delay
            
            # Create orchestrator
            self.orchestrator = GeminiOrchestratorWithTools(config)
            session_id = f"research_{int(time.time())}"
            self.orchestrator.set_session_id(session_id)
            self.current_session = session_id
            
            with self.results_output:
                print(f"✅ Orchestrator initialized (Session: {session_id})")
                print("🔄 Decomposing research query into subtasks...")
            
            # Progress callback
            def progress_callback(completed, total, last_result):
                if not self.is_running:  # Check if stopped
                    return
                    
                self.progress_bar.value = completed
                progress_percent = (completed / total) * 100
                
                status_html = f"""
                <b>Status:</b> 🔄 Processing agents ({completed}/{total} completed - {progress_percent:.1f}%)<br>
                <b>Session:</b> {session_id}<br>
                """
                
                if last_result:
                    status_color = "green" if last_result['status'] == 'success' else "red"
                    tools_used = len(last_result.get('tools_used', []))
                    status_html += f"<b>Last Agent:</b> <span style='color:{status_color}'>Agent {last_result['agent_id']} - {last_result['status']} ({tools_used} tools used)</span>"
                
                self.status_display.value = status_html
                
                with self.results_output:
                    if last_result:
                        status_icon = "✅" if last_result['status'] == 'success' else "❌"
                        tools_info = f" | {len(last_result.get('tools_used', []))} tools" if last_result.get('tools_used') else ""
                        print(f"{status_icon} Agent {last_result['agent_id']:2d} | {last_result['execution_time']:5.1f}s{tools_info} | {last_result['subtask'][:50]}...")
            
            # Run orchestration
            with self.results_output:
                print("\n🚀 Starting agent execution...")
                print("-" * 60)
            
            results = self.orchestrator.run_sequential_batch_orchestration(query, progress_callback)
            
            if not self.is_running:  # Check if stopped
                return
            
            # Display final results
            with self.results_output:
                print("\n" + "=" * 60)
                print("🎉 ORCHESTRATION COMPLETED SUCCESSFULLY!")
                print("=" * 60)
                
                stats = results['statistics']
                print(f"📊 EXECUTION STATISTICS:")
                print(f"   • Total Agents: {results['total_agents']}")
                print(f"   • ✅ Successful: {stats['completed']} ({stats['completed']/results['total_agents']*100:.1f}%)")
                print(f"   • ❌ Errors: {stats['errors']} ({stats['errors']/results['total_agents']*100:.1f}%)")
                print(f"   • ⏱️ Average Time: {stats['avg_duration']:.1f}s per agent")
                print(f"   • 🕒 Total Duration: {stats['total_duration']/60:.1f} minutes")
                
                print(f"\n📋 COMPREHENSIVE RESEARCH SYNTHESIS:")
                print("=" * 60)
                print(results['final_synthesis'])
                print("\n" + "=" * 60)
                print(f"💾 Results saved to session: {session_id}")
                print("🎯 Research orchestration complete!")
            
            self.status_display.value = f"<b>Status:</b> ✅ Completed! {stats['completed']}/{results['total_agents']} agents successful"
            self.progress_bar.bar_style = 'success'
            
        except Exception as e:
            with self.results_output:
                print(f"\n💥 ORCHESTRATION FAILED!")
                print(f"❌ Error: {str(e)}")
                print("\n🔧 Troubleshooting:")
                print("   1. Check your Gemini API key")
                print("   2. Verify internet connection")
                print("   3. Try reducing the number of agents")
                print("   4. Check Google Drive permissions")
            
            self.status_display.value = f"<b>Status:</b> ❌ Failed: {str(e)[:100]}..."
            self.progress_bar.bar_style = 'danger'
        
        finally:
            # Reset UI state
            self.is_running = False
            self.start_button.disabled = False
            self.stop_button.disabled = True
    
    # Start in background thread
    thread = threading.Thread(target=run_orchestration)
    thread.daemon = True
    thread.start()

def stop_orchestration(self, button):
    """Stop the current orchestration"""
    self.is_running = False
    
    with self.results_output:
        print("\n⏹️ STOPPING ORCHESTRATION...")
        print("💾 Progress has been automatically saved to checkpoint")
        print("🔄 You can resume this session later if needed")
    
    self.start_button.disabled = False
    self.stop_button.disabled = True
    self.status_display.value = "<b>Status:</b> ⏹️ Stopped by user (progress saved)"
    self.progress_bar.bar_style = 'warning'

def _estimate_duration(self, num_agents):
    """Estimate orchestration duration in minutes"""
    avg_agent_time = 3  # minutes
    concurrent = self.concurrent_slider.value
    batch_delay = self.delay_slider.value
    
    execution_time = (num_agents / concurrent) * avg_agent_time
    delay_time = (num_agents / concurrent) * (batch_delay / 60)
    
    return int(execution_time + delay_time)

# Add methods to the class
ResearchInterface.start_orchestration = start_orchestration
ResearchInterface.stop_orchestration = stop_orchestration
ResearchInterface._estimate_duration = _estimate_duration

print("✅ Orchestration logic added to interface")
#%%
# ============== LAUNCH THE INTERFACE ==============
print("\n" + "=" * 80)
print("🎯 LAUNCHING ENHANCED MAKE IT HEAVY RESEARCH INTERFACE")
print("=" * 80)
print("🚀 Large-Scale Research Orchestration System")
print("📚 Powered by DBLP Academic Search (No Rate Limits!)")
print("🧠 6 Advanced Research Tools Integrated")
print("💾 Automatic Checkpoint Management")
print("=" * 80)

# Create and display the interface
research_interface = ResearchInterface()
research_interface.display_interface()

print("\n🎉 INTERFACE READY!")
print("👆 Use the interface above to start your research orchestration!")
#%% md
## 🧪 **Quick Test Functions**

If you prefer to run orchestration programmatically instead of using the GUI above, you can use these functions:
#%%
# ============== QUICK TEST FUNCTIONS ==============

def test_small_scale_orchestration(query: str, num_agents: int = 5):
    """Quick test function for small-scale orchestration"""
    print(f"🧪 Testing {num_agents}-agent orchestration...")
    print(f"📝 Query: {query}")
    
    # Create test configuration
    config = CONFIG.copy()
    config['orchestrator']['parallel_agents'] = num_agents
    config['orchestrator']['max_concurrent'] = 2
    config['orchestrator']['batch_delay'] = 3
    
    # Create orchestrator
    orchestrator = GeminiOrchestratorWithTools(config)
    session_id = f"test_{int(time.time())}"
    orchestrator.set_session_id(session_id)
    
    # Run orchestration
    start_time = time.time()
    results = orchestrator.run_sequential_batch_orchestration(query)
    end_time = time.time()
    
    # Display results
    print(f"\n✅ Test completed in {(end_time - start_time)/60:.1f} minutes")
    print(f"📊 Success rate: {results['statistics']['completed']}/{results['total_agents']}")
    print(f"\n📋 Synthesis preview:")
    print(results['final_synthesis'][:500] + "...")
    
    return results

def run_large_scale_research_orchestration(query: str, num_agents: int = 100, 
                                         max_concurrent: int = 2, batch_delay: int = 5,
                                         resume_session: str = None):
    """Run full-scale research orchestration"""
    print(f"🚀 Starting {num_agents}-agent research orchestration...")
    print(f"📝 Query: {query}")
    print(f"⚙️ Config: {max_concurrent} concurrent, {batch_delay}s delay")
    
    # Create configuration
    config = CONFIG.copy()
    config['orchestrator']['parallel_agents'] = num_agents
    config['orchestrator']['max_concurrent'] = max_concurrent
    config['orchestrator']['batch_delay'] = batch_delay
    
    # Create orchestrator
    orchestrator = GeminiOrchestratorWithTools(config)
    
    if resume_session:
        orchestrator.set_session_id(resume_session)
        print(f"🔄 Resuming session: {resume_session}")
    else:
        session_id = f"research_{int(time.time())}"
        orchestrator.set_session_id(session_id)
        print(f"🆕 New session: {session_id}")
    
    # Progress callback
    def progress_callback(completed, total, last_result):
        if completed % 5 == 0 or completed == total:  # Update every 5 agents
            print(f"📊 Progress: {completed}/{total} agents completed ({completed/total*100:.1f}%)")
    
    # Run orchestration
    start_time = time.time()
    results = orchestrator.run_sequential_batch_orchestration(query, progress_callback)
    end_time = time.time()
    
    # Display final results
    print(f"\n🎉 Large-scale orchestration completed!")
    print(f"⏱️ Total time: {(end_time - start_time)/60:.1f} minutes")
    print(f"📊 Statistics: {results['statistics']}")
    print(f"\n📋 Final synthesis:")
    print(results['final_synthesis'])
    
    return results

def list_available_checkpoints():
    """List available checkpoint sessions"""
    checkpoint_manager = CheckpointManager()
    sessions = checkpoint_manager.list_checkpoints()
    
    if sessions:
        print(f"📂 Available checkpoint sessions ({len(sessions)}):")
        for session in sessions[-10:]:  # Show last 10
            print(f"   • {session}")
    else:
        print("📂 No checkpoint sessions found")
    
    return sessions

def load_checkpoint_results(session_id: str):
    """Load and display results from a checkpoint"""
    checkpoint_manager = CheckpointManager()
    data = checkpoint_manager.load_checkpoint(session_id)
    
    if data:
        print(f"📂 Loaded session: {session_id}")
        print(f"📝 Query: {data.get('user_input', 'Unknown')}")
        print(f"📊 Progress: {len(data.get('completed_agents', []))}/{data.get('total_agents', 0)} agents")
        
        if 'final_synthesis' in data:
            print(f"\n📋 Final synthesis:")
            print(data['final_synthesis'])
        else:
            print("⚠️ Session not yet completed")
    else:
        print(f"❌ Session not found: {session_id}")
    
    return data

print("✅ Quick test functions loaded")
print("\n🧪 Available functions:")
print("   • test_small_scale_orchestration(query, num_agents=5)")
print("   • run_large_scale_research_orchestration(query, num_agents=100)")
print("   • list_available_checkpoints()")
print("   • load_checkpoint_results(session_id)")
#%% md
## 🐙 **GitHub Research Tool Demo**

**NEW FEATURE**: The enhanced system now includes GitHub repository search and analysis!

This tool allows agents to:
- 🔍 **Find paper implementations** on GitHub
- 💻 **Analyze code repositories** and structure
- 📄 **Extract paper links** from README files
- ⭐ **Assess repository quality** with relevance scoring
- 🔗 **Bridge papers and code** for complete research analysis
#%%
# ============== GITHUB RESEARCH TOOL DEMO ==============

def demo_github_research_tool():
    """Demonstrate GitHub research tool capabilities"""
    print("🐙 GITHUB RESEARCH TOOL DEMONSTRATION")
    print("=" * 60)
    
    # Initialize the tool
    github_tool = GitHubResearchTool(CONFIG)
    
    # Demo 1: Search for paper implementations
    print("\n📋 Demo 1: Searching for Paper Implementations")
    print("-" * 50)
    
    demo_query = "dataset distillation"  # Single query for demo
    print(f"\n🔍 Searching for: '{demo_query}'")
    
    try:
        result = github_tool.execute(
            query=demo_query,
            search_type="papers",
            language="python",
            limit=3
        )
        
        if result['status'] == 'success':
            repos = result.get('repositories', [])
            print(f"   ✅ Found {len(repos)} implementations")
            
            for i, repo in enumerate(repos, 1):
                score = repo.get('paper_relevance_score', 0)
                print(f"   {i}. {repo['full_name']}")
                print(f"      ⭐ {repo['stars']} stars | 🍴 {repo['forks']} forks | 📊 Score: {score:.2f}")
                print(f"      📝 {repo['description'][:80]}...")
                
                if repo.get('paper_links'):
                    print(f"      📄 Paper links: {len(repo['paper_links'])} found")
                
                if repo.get('key_files'):
                    files = ', '.join(repo['key_files'][:3])
                    print(f"      💻 Key files: {files}")
                print()
        else:
            error_msg = result.get('error', 'Unknown error')
            if 'rate limit' in error_msg.lower():
                print(f"   ⚠️ Rate limited - add GitHub token for higher limits")
                print(f"   💡 Get token at: https://github.com/settings/tokens")
            else:
                print(f"   ❌ Search failed: {error_msg}")
                
    except Exception as e:
        print(f"   💥 Demo failed: {str(e)}")
    
    # Summary
    print("\n📊 GITHUB TOOL CAPABILITIES SUMMARY")
    print("=" * 60)
    print("✅ Paper Implementation Search - Find repos that implement research papers")
    print("✅ Code File Search - Find specific code implementations")
    print("✅ Repository Analysis - Extract README, paper links, key files")
    print("✅ Quality Scoring - AI-powered relevance assessment")
    print("✅ Agent Integration - Automatic tool selection in orchestration")
    
    print("\n🚀 INTEGRATION WITH ORCHESTRATION")
    print("=" * 60)
    print("When agents encounter queries about implementations, they will:")
    print("1. 📚 Search DBLP for academic papers")
    print("2. 💻 Search GitHub for code implementations")
    print("3. 🔍 Analyze repository quality and relevance")
    print("4. 📋 Synthesize papers + code into comprehensive analysis")
    
    print("\n💡 RATE LIMITS & AUTHENTICATION")
    print("=" * 60)
    github_token = CONFIG.get('github', {}).get('token')
    if github_token and github_token != 'YOUR_GITHUB_TOKEN':
        print("✅ GitHub token configured - 5,000 requests/hour")
        print("🚀 Ready for large-scale orchestration!")
    else:
        print("⚠️ No GitHub token - 60 requests/hour limit")
        print("💡 Add token to CONFIG['github']['token'] for higher limits")
        print("🔗 Get token: https://github.com/settings/tokens")
    
    print("\n🎯 READY FOR ORCHESTRATION!")
    print("=" * 60)
    print("The GitHub tool is now integrated and ready to use.")
    print("Try queries like:")
    print("  • 'Find implementations of transformer attention mechanisms'")
    print("  • 'What are the official PyTorch implementations of dataset distillation?'")
    print("  • 'Show me code repositories for neural architecture search'")

# Run the demonstration
demo_github_research_tool()