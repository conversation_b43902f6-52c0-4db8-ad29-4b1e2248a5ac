# COLAB TIMEOUT FIX
# This fixes the HTTPConnectionPool timeout issue and button binding

import google.generativeai as genai
import time
import threading

# Fix 1: Robust Gemini API initialization with timeout handling
def initialize_gemini_with_timeout_fix(config):
    """Initialize Gemini API with timeout error handling"""
    try:
        genai.configure(api_key=config['gemini']['api_key'])
        model = genai.GenerativeModel(config['gemini']['model'])
        
        # Try a quick test with shorter timeout
        try:
            test_response = model.generate_content("Test", 
                                                 generation_config=genai.types.GenerationConfig(
                                                     max_output_tokens=10,
                                                     temperature=0.1
                                                 ))
            if test_response.text:
                print("✅ Gemini API connection successful")
                return model
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['localhost', '41601', 'httpconnectionpool', 'timeout']):
                print("⚠️ Detected Colab proxy/timeout issue. Continuing without test...")
                print("   The API should work for actual requests.")
                return model  # Return model anyway, it might work for real requests
            else:
                raise e
                
    except Exception as e:
        raise RuntimeError(f"Failed to initialize Gemini API: {e}")

# Fix 2: Robust task decomposition with timeout handling
def decompose_task_with_timeout_fix(model, user_input, num_agents):
    """Generate questions with timeout error handling"""
    prompt = f"""Generate exactly {num_agents} different, specific research questions that would help thoroughly analyze: {user_input}

Each question should approach the topic from a different angle:
- Literature review and background
- Technical methodology analysis
- Novel idea generation
- Feasibility assessment
- Critical evaluation
- Comparative analysis

Return your response as a JSON array of strings, like this:
["question 1", "question 2", "question 3"]

Only return the JSON array, nothing else."""

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Use shorter timeout and simpler generation config
            response = model.generate_content(prompt,
                                            generation_config=genai.types.GenerationConfig(
                                                max_output_tokens=1000,
                                                temperature=0.7
                                            ))
            if response.text:
                import json
                questions = json.loads(response.text.strip())
                if isinstance(questions, list) and len(questions) >= num_agents:
                    return questions[:num_agents]
                else:
                    raise ValueError(f"Expected {num_agents} questions, got {len(questions) if isinstance(questions, list) else 'invalid format'}")
            else:
                raise ValueError("Empty response from Gemini API")
                
        except Exception as e:
            if any(keyword in str(e).lower() for keyword in ['localhost', '41601', 'httpconnectionpool', 'timeout']):
                print(f"⚠️ Timeout/proxy error on attempt {attempt + 1}/{max_retries}")
                if attempt == max_retries - 1:
                    print("🔄 Using fallback question generation due to connection issues")
                    break
                else:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    continue
            elif "json" in str(e).lower():
                if attempt == max_retries - 1:
                    print(f"⚠️ JSON parsing failed after {max_retries} attempts, using fallback")
                    break
                else:
                    print(f"⚠️ JSON parsing failed, retrying... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(2)
                    continue
            else:
                if attempt == max_retries - 1:
                    print(f"⚠️ Task decomposition failed after {max_retries} attempts: {e}")
                    break
                else:
                    print(f"⚠️ Task decomposition failed, retrying... (attempt {attempt + 1}/{max_retries}): {e}")
                    time.sleep(2)
                    continue
    
    # Fallback question generation
    print("🔄 Using fallback question generation")
    base_questions = [
        f"Research comprehensive literature review about: {user_input}",
        f"Analyze technical methodologies and approaches for: {user_input}",
        f"Generate novel research ideas related to: {user_input}",
        f"Assess feasibility and practical implementation of: {user_input}",
        f"Provide critical evaluation and limitations of: {user_input}",
        f"Compare different approaches and alternatives for: {user_input}",
        f"Identify research gaps and future directions in: {user_input}",
        f"Examine real-world applications and use cases of: {user_input}"
    ]
    
    return base_questions[:num_agents]

# Fix 3: Button binding fix for GUI
def fix_button_binding(interface_instance):
    """Fix the button binding issue"""
    def start_wrapper(button):
        try:
            interface_instance.start_orchestration(button)
        except Exception as e:
            print(f"❌ Error starting orchestration: {e}")
            
    def stop_wrapper(button):
        try:
            interface_instance.stop_orchestration(button)
        except Exception as e:
            print(f"❌ Error stopping orchestration: {e}")
    
    # Re-bind the buttons
    interface_instance.start_button.on_click(start_wrapper)
    interface_instance.stop_button.on_click(stop_wrapper)
    
    print("✅ Button bindings fixed")

print("🔧 Colab timeout and button fixes loaded")
print("Use these functions to fix the specific issues:")
print("  - initialize_gemini_with_timeout_fix(CONFIG)")
print("  - decompose_task_with_timeout_fix(model, query, num_agents)")
print("  - fix_button_binding(interface_instance)")